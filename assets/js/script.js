document.addEventListener('DOMContentLoaded', function () {
    // Ukryj elementy po załadowaniu strony
    var additionalInfo = document.querySelector('.additional-info');
    if (additionalInfo) {
        additionalInfo.style.display = 'none';
    }
});

document.addEventListener('DOMContentLoaded', function () {
    var searchButton = document.getElementById('search-button');
    var searchInput = document.getElementById('search-input');

    if (searchButton && searchInput) {
        searchButton.addEventListener('click', function (event) {
            if (searchInput.value.trim() === '') {
                event.preventDefault(); // Zapobiega wysłaniu formularza, jeśli pole jest puste
            }
        });
    }
});

// Akordeon
jQuery(document).ready(function ($) {
    // Domyślnie zamknij wszystkie akordeony
    $('.checkout-accordion-content').hide();

    // Obsługa akordeonów
    $('.checkout-accordion-header').on('click', function () {
        $(this).next('.checkout-accordion-content').slideToggle();
        $(this).toggleClass('active');
    });
});

document.addEventListener('DOMContentLoaded', function () {
    var accordions = document.querySelectorAll('.product-accordion');
    accordions.forEach(function(accordion) {
        accordion.addEventListener('click', function() {
            var content = this.nextElementSibling;
            content.classList.toggle('active');
        });
    });

    var toggles = document.querySelectorAll('.accordion-toggle');
    toggles.forEach(function (toggle) {
        toggle.addEventListener('click', function () {
            this.classList.toggle('active');

            var content = this.nextElementSibling;
            if (content.classList.contains('active')) {
                content.classList.remove('active');
            } else {
                content.classList.add('active');
            }
        });
    });
});



document.addEventListener("DOMContentLoaded", function () {
    var menuToggle = document.querySelector(".menu-toggle");
    var mobileMenu = document.querySelector("#mobile-menu");

    // Dodajemy event listener do hamburgera
    menuToggle.addEventListener("click", function () {
        mobileMenu.classList.toggle("hidden");
    });
});
document.addEventListener('DOMContentLoaded', function () {
    var cartTrigger = document.querySelector('.cart-trigger');
    var miniCart = document.getElementById('mini-cart');

    // Toggle visibility of the mini cart when cart trigger is clicked
    cartTrigger.addEventListener('click', function (e) {
        e.preventDefault();
        if (miniCart.style.display === 'none' || miniCart.style.display === '') {
            miniCart.style.display = 'block';
        } else {
            miniCart.style.display = 'none';
        }
    });

    // Hide the mini cart when clicking outside of it
    document.addEventListener('click', function (e) {
        if (!miniCart.contains(e.target) && !cartTrigger.contains(e.target)) {
            miniCart.style.display = 'none';
        }
    });
});
document.addEventListener('DOMContentLoaded', function () {
    var cartTrigger = document.querySelector('.mobile-cart-trigger');
    var miniCart = document.getElementById('mini-cart');

    if (cartTrigger && miniCart) {
        cartTrigger.addEventListener('click', function () {
            console.log('Cart trigger clicked');
            if (miniCart.style.display === 'none' || miniCart.style.display === '') {
                miniCart.style.display = 'block';
            } else {
                miniCart.style.display = 'none';
            }
        });
    } else {
        console.error('Element(s) not found: .mobile-cart-trigger or #mini-cart');
    }
})

// SLIDER
jQuery(document).ready(function ($) {
    $('.slider').slick({
        dots: true,  // Pokaż nawigacyjne kropki
        infinite: true,  // Bezkończoność przewijania
        speed: 500,  // Prędkość animacji
        slidesToShow: 4,  // Liczba wyświetlanych slajdów naraz
        slidesToScroll: 1,  // Liczba przewijanych slajdów naraz
        autoplay: true,  // Automatyczne przewijanie
        autoplaySpeed: 3000,  // Przerwa między automatycznym przewijaniem
        arrows: false,  // Pokazuje strzałki nawigacyjne
        adaptiveHeight: true,  // Automatyczne dopasowanie wysokości do treści
        responsive: [  // Ustawienia responsywne
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,  // Na mniejszych ekranach pokaż 3 slajdy
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 991,
                settings: {
                    slidesToShow: 1,  // Na tabletach pokaż 2 slajdy
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,  // Na telefonach pokaż 1 slajd
                    slidesToScroll: 1
                }
            }
        ]
    });
});

jQuery(document).ready(function ($) {
    $('.partnerzy-slider').slick({
        slidesToShow: 6,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 2000,
        dots: true,
        arrows: false,
        responsive: [
            {
                breakpoint: 991,  // szerokość ekranu poniżej 991px
                settings: {
                    slidesToShow: 3,  // pokaż 3 obrazy
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 768,  // szerokość ekranu poniżej 480px
                settings: {
                    slidesToShow: 1,  // pokaż 1 obraz dla mniejszych ekranów
                    slidesToScroll: 1
                }
            }
        ]
    });
});

// WOOCOMMERCE

function updateMiniCart() {
    $.ajax({
        type: 'GET',
        url: wc_add_to_cart_params.ajax_url,
        data: {
            action: 'woocommerce_get_mini_cart_content'
        },
        success: function(response) {
            $('.mini-cart').html(response);
            updateMiniCartItemsShorthand();
        },
        error: function(xhr, status, error) {
            console.error("Błąd AJAX podczas aktualizacji mini-koszyka:", error);
        }
    });
}

function updateMiniCartItemsShorthand() {
    let total = 0;
    document.querySelectorAll('#mini-cart .cart-quantity input').forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
    });

    document.querySelector('.dd-header-cart-item').innerText = `Koszyk (${total})`;
}

// Obsługuje kliknięcia przycisku "Dodaj do koszyka"
jQuery(document).ready(function ($) {
    $(document).on('click', 'button.single_add_to_cart_button', function (e) {
        e.preventDefault();

        var $button = $(this),
            $form = $button.closest('form.cart'),
            productID = $button.val(),
            quantity = $form.find('input[name=quantity]').val() || 1;

        let productName = $(this).data('product-name');
        let productPrice = $(this).data('product-price');
        let productSku = $(this).data('sku');

        var data = {
            action: 'woocommerce_add_to_cart',
            product_id: productID,
            quantity: quantity
        };

        $.ajax({
            type: 'POST',
            url: wc_add_to_cart_params.ajax_url,
            data: data,
            beforeSend: function () {
                $button.addClass('loading');
            },
            success: function (response) {
                $button.removeClass('loading');

                if (response.error) {
                    alert("Nie udało się dodać produktu do koszyka. Jest on aktualnie niedostępny. Spróbuj ponownie później.");
                    return;
                }

                updateMiniCart();
                $('.mini-cart').fadeIn();

                // Send GTM event for "add to cart"
                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                    event: 'add_to_cart',
                    ecommerce: {
                        items: [{
                            item_id: productSku ?? productID,
                            item_name: productName,
                            quantity: quantity,
                            price: productPrice
                        }]
                    }
                });
            },
            error: function (xhr, status, error) {
                console.error("Błąd AJAX:", error);
                alert('Błąd podczas dodawania produktu do koszyka.');
                $button.removeClass('loading');
            }
        });

        return false;
    });

    // Zmiana ilości produktu
    $(document).on('change', '.cart-quantity input', function() {
        var cart_item_key = $(this).closest('li.cart-item').data('item-key');
        var quantity = parseInt($(this).val(), 10);
        var max_quantity = parseInt($(this).attr('max'), 10);

        if (quantity > max_quantity) {
            alert('Nie możesz wybrać więcej niż ' + max_quantity + ' sztuk tego produktu.');
            $(this).val(max_quantity);
            quantity = max_quantity;
        }

        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'update_cart',
                cart_item_key: cart_item_key,
                quantity: quantity
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                    $('.mini-cart').html(response.data.html);
                    updateMiniCartItemsShorthand();

                } else {
                    alert('Wystąpił błąd podczas przeliczania koszyka.');
                    console.error("Błąd AJAX:", error);
                }
            }
        });
    });

    // Usunięcie produktu z koszyka
    $(document).on('click', '.cart-remove a.remove-item', function(e) {
        e.preventDefault();
        var cart_item_key = $(this).closest('li.cart-item').data('item-key');
        var $item = $(this).closest('li.cart-item');

        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'remove_from_cart',
                cart_item_key: cart_item_key
            },
            success: function(response) {
                if (response.success) {
                    $item.fadeOut('slow', function() {
                        $(this).remove();
                    });

                    updateMiniCart();

                    if (response.data.cart_item_count === 0) {
                        window.location.href = '/';
                    } else {
                        location.reload();
                    }
                } else {
                    alert('Wystąpił błąd podczas usuwania produktu.');
                    console.error("Błąd AJAX:", error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Błąd AJAX: ', error);
                alert('Wystąpił błąd podczas próby usunięcia produktu.');
            }
        });
    });
});

jQuery(document).ready(function ($) {
    // Obsługa przycisku minus
    $('.minus').on('click', function () {
        var qty = $(this).closest('.quantity').find('.qty');
        var val = parseInt(qty.val());
        var min = parseInt(qty.attr('min'));

        if (!isNaN(val) && val > min) {
            qty.val(val - 1).change();
        }
    });

    // Obsługa przycisku plus
    $('.plus').on('click', function () {
        var qty = $(this).closest('.quantity').find('.qty');
        var val = parseInt(qty.val());
        var max = parseInt(qty.attr('max'));

        if (!isNaN(val) && (!max || val < max)) {
            qty.val(val + 1).change();
        }
    });

    // Monitoruj zmiany w polu ilości i aktualizuj ukryte pole formularza
    $('input.qty').on('change', function () {
        var form = $(this).closest('form');
        var addToCartButton = form.find('.single_add_to_cart_button');
        var productID = addToCartButton.val();
        var quantity = $(this).val();

        // Aktualizacja ukrytych pól lub innych elementów formularza
        addToCartButton.data('quantity', quantity);
    });
})

document.addEventListener("DOMContentLoaded", function () {
    var searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.placeholder = searchInput.placeholder.toLowerCase();
    }
});
document.addEventListener('DOMContentLoaded', function () {
    var cartTrigger = document.querySelector('.mobile-cart-trigger');
    var miniCart = document.getElementById('mobile-mini-cart');

    if (cartTrigger && miniCart) {
        cartTrigger.addEventListener('click', function () {
            // Toggle visibility of the mini cart
            if (miniCart.style.display === 'none' || miniCart.style.display === '') {
                miniCart.style.display = 'block';
            } else {
                miniCart.style.display = 'none';
            }
        });
    } else {
        console.error('Element(s) not found: .mobile-cart-trigger or #mobile-mini-cart');
    }
});

// VARIATIONS //

let mainProductDescription = '';

function updateProductDetails(tile, productContainer) {
    const variationId = tile.getAttribute('data-variation-id');
    const priceHtml = tile.getAttribute('data-price-html');
    const priceNumber = tile.getAttribute('data-price-number');
    const variationSku = tile.getAttribute('data-sku');
    const variationDescription = tile.getAttribute('data-description');
    const variationImageUrl = tile.getAttribute('data-image-url');
    const variationStockQuantity = tile.getAttribute('data-stock-quantity');

    const priceElement = productContainer.querySelector('.price, .product-price');
    const addToCartButton = productContainer.querySelector('.single_add_to_cart_button');
    const quantityDiv = productContainer.querySelector('.quantity');
    const descriptionElement = productContainer.querySelector('.woocommerce-product-details__short-description');
    const imageElement = productContainer.querySelector('.product-images img');
    const availabilityElements = productContainer.querySelectorAll('.dd-quantity-available');

    if (priceElement) priceElement.innerHTML = priceHtml;

    if (!mainProductDescription && descriptionElement) {
        mainProductDescription = descriptionElement.innerHTML;
    }

    if (descriptionElement) {
        if (variationDescription && variationDescription.trim() !== '') {
            descriptionElement.innerHTML = '<p>' + variationDescription + '</p>';
        } else {
            descriptionElement.innerHTML = '<p>' + mainProductDescription + '</p>';
        }
    }

    if (imageElement && variationImageUrl) {
        imageElement.src = variationImageUrl;
    }

    if (addToCartButton) {
        addToCartButton.style.display = 'inline-block';
        addToCartButton.setAttribute('value', variationId);
        addToCartButton.dataset.productPrice = priceNumber;
        addToCartButton.dataset.sku = variationSku;
    }

    if (document.body.classList.contains('single-product')) {
        if (variationStockQuantity <= 0) {
            if (addToCartButton) addToCartButton.style.display = 'none';
            if (quantityDiv) quantityDiv.style.display = 'none';
        } else {
            if (addToCartButton) addToCartButton.style.display = 'inline-block';
            if (quantityDiv) {
                quantityDiv.style.display = 'inline-flex';
                const quantityInput = quantityDiv.querySelector('.qty');
                quantityInput.value = 1;
                quantityInput.setAttribute('max', variationStockQuantity);
            }
        }
    }

    availabilityElements.forEach(element => {
        if (element.getAttribute('data-variation-id') === variationId) {
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });
}

// Funkcja obsługująca kliknięcie na wariant produktu
function handleVariationClick(event) {
    const tile = event.currentTarget;
    const productContainer = tile.closest('.product-item') || tile.closest('.product');

    if (productContainer) {
        updateProductDetails(tile, productContainer);
    }
}

// Funkcja do przycisków wyboru wariantów
function initializeVariationButtons() {
    const productContainers = document.querySelectorAll('.product-item, .product');

    productContainers.forEach(container => {
        const variationTiles = container.querySelectorAll('.variation-button');

        if (variationTiles.length > 0) {
            // Znajdź pierwszy wariant z dostępnością większą od 0
            let defaultVariation = variationTiles[0];

            for (let i = 0; i < variationTiles.length; i++) {
                const stockQuantity = parseInt(variationTiles[i].getAttribute('data-stock-quantity'), 10);
                if (stockQuantity > 0) {
                    defaultVariation = variationTiles[i];
                    break;
                }
            }

            // Zaznacz domyślny wariant
            defaultVariation.classList.add('checked');
            updateProductDetails(defaultVariation, container);

            variationTiles.forEach(tile => {
                tile.addEventListener('click', function () {
                    variationTiles.forEach(b => b.classList.remove('checked'));
                    this.classList.add('checked');
                    updateProductDetails(this, container);
                });
            });
        }
    });
}

document.addEventListener("DOMContentLoaded", function() {
    initializeVariationButtons();
});

document.addEventListener("DOMContentLoaded", function() {
    const buttons = document.querySelectorAll('.variation-button');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const productContainer = this.closest('.product-item') || this.closest('.product');
            const productButtons = productContainer.querySelectorAll('.variation-button');

            productButtons.forEach(b => b.classList.remove('checked'));
            this.classList.add('checked');
        });
    });

    const productContainers = document.querySelectorAll('.product-item, .product');
    productContainers.forEach(container => {
        const defaultVariationButton = container.querySelector('.variation-button.checked');
        if (defaultVariationButton) {
            defaultVariationButton.click();
        }
    });
});

// Funkcja sprawdza, czy produkt ma warianty i obsługująca wyświetlanie przycisku "Dodaj do koszyka" na archiwum
function handleProductHover() {
    const productItems = document.querySelectorAll('.product-item');

    productItems.forEach(item => {
        const variationTiles = item.querySelectorAll('.variation-button');
        const addToCartButton = item.querySelector('.add-to-cart-button');

        if (variationTiles.length === 0 && addToCartButton) {
            item.addEventListener('mouseenter', () => {
                addToCartButton.style.display = 'inline-block';
            });
            item.addEventListener('mouseleave', () => {
                addToCartButton.style.display = 'none';
            });
        }
    });
}

if (document.body.matches('.single-product')) {
    initializeVariationButtons();
} else if (document.body.matches('.archive, .home, .search, .blog')) {
    handleProductHover();
    initializeVariationButtons();
}

// Po najechaniu na produkt ustaw element na active
document.addEventListener("DOMContentLoaded", function () {
    const productItems = document.querySelectorAll('.product-item');

    function activateProduct(item) {
        productItems.forEach(product => {
            if (product !== item) {
                product.classList.remove('active');
            }
        });
        item.classList.add('active');
    }

    function deactivateProducts(event) {
        productItems.forEach(item => {
            if (!item.contains(event.target)) {
                item.classList.remove('active');
            }
        });
    }

    productItems.forEach(item => {
        item.addEventListener('mouseenter', () => activateProduct(item));
    });

    document.addEventListener('click', deactivateProducts);
});

// Rozmiary obrazów w produkcie
document.addEventListener("DOMContentLoaded", function() {
    const mainImage = document.getElementById('main-product-image');
    const thumbnails = document.querySelectorAll('.product-thumbnails img');

    thumbnails.forEach(function(thumbnail) {
        thumbnail.addEventListener('click', function() {
            let mainImageSrc = mainImage.src;
            mainImage.src = this.getAttribute('data-fullsize');

            jQuery('.product-thumbnails img.dd-thumbnail-active').removeClass('dd-thumbnail-active');
            this.classList.add('dd-thumbnail-active');
        });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    const mobileMenu = document.querySelector(".mobile-nav-menu");

    const toggleMenu = (parentLi) => {
        parentLi.classList.toggle("menu-open");
        parentLi.parentNode.querySelectorAll(":scope > .menu-item-has-children.menu-open")
            .forEach(sibling => sibling !== parentLi && sibling.classList.remove("menu-open"));
    };

    mobileMenu.addEventListener("click", function (e) {
        const menuItemLink = e.target.closest(".menu-item-has-children > a");

        if (menuItemLink) {
            e.preventDefault();
            toggleMenu(menuItemLink.parentNode);
        }
    });
});

document.addEventListener('DOMContentLoaded', () => {
    const tabs = document.querySelectorAll('.tab');
    const tabPanels = document.querySelectorAll('.tab-panel');

    const activateTab = (tab) => {
        const tabId = tab.dataset.tab;
        const activePanel = document.querySelector(`.tab-panel[data-tab="${tabId}"]`);

        tabs.forEach(t => t.classList.remove('active'));
        tabPanels.forEach(panel => panel.classList.remove('active', 'fade-in'));

        tab.classList.add('active');
        if (activePanel) {
            activePanel.classList.add('active');
            activePanel.classList.add('fade-in');
        }
    };

    tabs.forEach(tab => tab.addEventListener('click', () => activateTab(tab)));

    const initialActiveTab = document.querySelector('.tab.active');
    if (initialActiveTab) {
        activateTab(initialActiveTab);
    }
});

document.addEventListener("DOMContentLoaded", () => {
    const cards = document.querySelectorAll(".card");

    const closeAllCards = () => {
        cards.forEach(card => card.classList.remove("open"));
    };

    const handleCardClick = (card) => {
        const isOpen = card.classList.contains("open");
        closeAllCards();

        if (!isOpen) {
            card.classList.add("open");
        }
    };

    cards.forEach(card => {
        const icon = card.querySelector(".icon");
        if (icon) {
            icon.addEventListener("click", (e) => {
                e.preventDefault();
                handleCardClick(card);
            });
        }
    });

    const cardToOpenDefault = cards[0];
    if (cardToOpenDefault) {
        cardToOpenDefault.classList.add("open");
    }
});

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".product-link").forEach((link) => {
        link.addEventListener("mouseenter", function () {
            const secondImage = this.querySelector(".dd-product-second-image");
            if (secondImage && secondImage.classList.contains("lazy-hidden")) {
                secondImage.src = secondImage.dataset.src;
                secondImage.classList.remove("lazy-hidden");
            }
        });
    });
});