jQuery(document).ready(function($) {

    function updateQuantityDiscountTable() {
        $('.quantity-discount-table-wrapper').hide();

        // Szukaj aktywnego wariantu (z klasą 'checked' lub 'selected')
        const activeVariation = $('.variation-button.checked, .variation-button.selected').first();

        // Debug - usuń po testach
        console.log('Quantity Discounts Debug:', {
            'activeVariation': activeVariation.length,
            'variationId': activeVariation.length ? activeVariation.data('variation-id') : 'none',
            'allVariations': $('.variation-button').length,
            'checkedVariations': $('.variation-button.checked').length,
            'selectedVariations': $('.variation-button.selected').length,
            'discountTables': $('.quantity-discount-table-wrapper').length
        });

        if (activeVariation.length) {
            const selectedVariationId = activeVariation.data('variation-id');
            const tableWrapper = $('.quantity-discount-table-wrapper[data-variation-id="' + selectedVariationId + '"]');
            if (tableWrapper.length) {
                tableWrapper.show();
                console.log('Showing table for variation:', selectedVariationId);
            } else {
                console.log('No table found for variation:', selectedVariationId);
            }
        } else {
            // Jeśli nie ma aktywnego wariantu, pokaż tabelkę dla produktu głównego
            const mainTable = $('.quantity-discount-table-wrapper:not([data-variation-id])');
            if (mainTable.length) {
                mainTable.show();
                console.log('Showing main product table');
            } else {
                console.log('No main product table found');
            }
        }
    }

    // Obsługa kliknięcia w wariant
    $(document).on('click', '.variation-button', function() {
        // Usuń poprzednie zaznaczenia
        $('.variation-button').removeClass('selected checked');
        // Dodaj zaznaczenie do klikniętego wariantu
        $(this).addClass('checked selected');

        setTimeout(updateQuantityDiscountTable, 100);
    });

    // Integracja z istniejącą funkcją updateProductDetails
    if (typeof window.originalUpdateProductDetails === 'undefined') {
        window.originalUpdateProductDetails = window.updateProductDetails;
    }

    window.updateProductDetails = function(tile, productContainer) {
        if (window.originalUpdateProductDetails) {
            window.originalUpdateProductDetails(tile, productContainer);
        }
        // Dodaj opóźnienie, aby upewnić się, że DOM został zaktualizowany
        setTimeout(updateQuantityDiscountTable, 150);
    };

    // Inicjalizacja przy załadowaniu strony
    function initializeQuantityDiscounts() {
        // Sprawdź czy istnieją warianty
        if ($('.variation-button').length > 0) {
            // Jeśli nie ma aktywnego wariantu, aktywuj pierwszy dostępny
            if (!$('.variation-button.checked, .variation-button.selected').length) {
                const firstAvailableVariation = $('.variation-button').not('.out-of-stock').first();
                if (firstAvailableVariation.length) {
                    firstAvailableVariation.addClass('checked');
                }
            }
        }
        updateQuantityDiscountTable();
    }

    // Uruchom inicjalizację po załadowaniu strony
    initializeQuantityDiscounts();

    // Uruchom ponownie po krótkim opóźnieniu (na wypadek, gdyby inne skrypty modyfikowały DOM)
    setTimeout(initializeQuantityDiscounts, 500);

    // Dodatkowe uruchomienie po dłuższym opóźnieniu dla pewności
    setTimeout(initializeQuantityDiscounts, 1000);

    // Obserwator mutacji DOM - reaguje na zmiany w strukturze strony
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && (node.classList.contains('variation-button') || node.classList.contains('quantity-discount-table-wrapper'))) {
                            shouldUpdate = true;
                        }
                        if (node.querySelector && (node.querySelector('.variation-button') || node.querySelector('.quantity-discount-table-wrapper'))) {
                            shouldUpdate = true;
                        }
                    }
                });
            }
            if (mutation.type === 'attributes' && mutation.target.classList && mutation.target.classList.contains('variation-button')) {
                shouldUpdate = true;
            }
        });

        if (shouldUpdate) {
            setTimeout(updateQuantityDiscountTable, 100);
        }
    });

    // Rozpocznij obserwację zmian w DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
    });

    $(document).on('change', '.qty', function() {
        const quantity = parseInt($(this).val()) || 1;
        updatePriceBasedOnQuantity(quantity);
    });

    function updatePriceBasedOnQuantity(quantity) {
        const selectedVariationId = $('.variation-button.selected').data('variation-id');

        if (selectedVariationId) {
            const tableWrapper = $('.quantity-discount-table-wrapper[data-variation-id="' + selectedVariationId + '"]');
            if (tableWrapper.length) {
                highlightApplicableDiscount(tableWrapper, quantity);
            }
        } else {
            const tableWrapper = $('.quantity-discount-table-wrapper:not([data-variation-id])');
            if (tableWrapper.length) {
                highlightApplicableDiscount(tableWrapper, quantity);
            }
        }
    }

    function highlightApplicableDiscount(tableWrapper, quantity) {
        const rows = tableWrapper.find('tbody tr');
        rows.removeClass('active-discount');

        rows.each(function() {
            const qtyText = $(this).find('td:first').text();
            const qtyRange = qtyText.replace(' szt.', '');

            let minQty, maxQty;
            if (qtyRange.includes('-')) {
                const parts = qtyRange.split('-');
                minQty = parseInt(parts[0]);
                maxQty = parseInt(parts[1]);
            } else if (qtyRange.includes('+')) {
                minQty = parseInt(qtyRange.replace('+', ''));
                maxQty = 999999;
            } else {
                minQty = maxQty = parseInt(qtyRange);
            }

            if (quantity >= minQty && quantity <= maxQty) {
                $(this).addClass('active-discount');
            }
        });
    }

    $('.quantity-discount-table').append('<style>.active-discount { background-color: #e8f5e8 !important; font-weight: bold; }</style>');
});
