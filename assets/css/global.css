/*
Theme Name: FitoSpa
Author: www.digitaldimension.pl
Description: Motyw dla strony FITOSPA
Requires at least: 5.3
Version: 1.0
*/

:root {
    --text-gray: #44403C;
    --text-gray-light: #615f59;
    --primary-green: #556964;
    --navbar-height: 155px;
    --accent-red: #B24837;
}

html, body {
    margin: 0 !important;
}

body {
    padding-top: var(--navbar-height);
    margin: 0;
}

.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6,
.entry-content h1,
.entry-content h2:not(.product-title),
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6 {
    font-family: 'Lato', sans-serif;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: #1e1e1e;
}

.page-content p {
    color: #171717;
    font-weight: 300;
    line-height: 1.75;
}

@media (max-width: 991.98px) {
    body {
        padding-top: 50px;
    }
}

.desktop-header {
    width: 100%;
    top: 0;
    position: fixed;
    z-index: 999;
    display: flex;
    flex-direction: column;
}

.mobile-header {
    display: none !important;
}

.header-block1 {
    width: 100%;
    background: var(--primary-green);
}

.header-block1-content {
    display: flex;
    top: 0;
    justify-content: right;
    height: 35px;
    gap: 25px;
    align-items: center !important;
    text-decoration: none;
    z-index: 1;

}

.header-block1 p {
    color: white;
    text-decoration: none;
    font-family: 'Lato', sans-serif;
    font-size: 14px;
    font-weight: 300;
}

.header-block1 a {
    color: white;
    text-decoration: none;
    font-family: 'Lato', sans-serif;
    font-size: 14px;
    font-weight: 300;
}

.header-block1 i {
    color: white;
    font-size: 4px;
}

.social-icon {
    margin-right: 25px;
}

.social-icon i {
    color: white;
    font-size: 16px;
    margin-left: 10px;
}

.social-icon i:hover {
    color: rgb(221, 221, 221);
}

.header-block1 p:hover {
    color: rgb(221, 221, 221);
}

.header.block2 {
    display: flex;
    justify-content: center;
    width: 100%;
    margin: 0 auto !important;
    background-color: white;
    height: 67px;
    align-items: center;
    align-content: center;
}

.header-block2-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: white;
    height: 67px;
    align-items: center;
    align-content: center;
    position: relative;
}

.header-block2-content .dd-navbar-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

@media (max-width: 960px) {
    #dd-navbar-search-box {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 20px;
    }

    .header-block2-content .dd-navbar-center {
        position: static;
        left: auto;
        transform: none;
        display: flex;
        justify-content: center;
    }
}

@media (min-width: 960px) and (max-width: 1250px) {
    #dd-navbar-search-box .search-form {
        width: 150px;
    }
    #dd-navbar-search-box #search-input {
        max-width: 120px;
    }
}

.dd-header-block-category-menu {
    width: 100%;
    background: #f8f8f8;
    border-bottom: 1px solid #eeeeee;
}

.dd-product-categories-menu ul {
    display: flex;
    padding-inline-start: 0;
    list-style: none;
    gap: 10px;
    margin: 0;
}

.dd-product-categories-menu li, .dd-product-categories-menu li a {
    padding-top: 16px;
    padding-bottom: 16px;
    font-weight: 300 !important;
}

.dd-product-categories-menu li:hover {
    cursor: pointer;
    color: #9a9a9a !important;
}

.dd-product-categories-menu ul.sub-menu {
    display: none;
    flex-direction: column;
    position: absolute;
    left: -25px;
    top: 50px;
    width: fit-content;
    background: #f8f8f8;
    border: 1px solid #eeeeee;
    gap: 0px;
}

.dd-product-categories-menu ul.sub-menu li {
    padding: 13px 50px 13px 25px;
    text-wrap: nowrap;
}

.dd-product-categories-menu ul.sub-menu li:not(:last-of-type) {
    border-bottom: 1px solid #eeeeee;
}

.dd-product-categories-menu ul.sub-menu li:hover {
    background: #dcdcdc;
}

.dd-product-categories-menu .menu-item-has-children {
    position: relative;
}

.dd-product-categories-menu .menu-item-has-children:hover .sub-menu {
    display: flex;
}

@media (max-width: 768px) {
    .dd-product-categories-menu ul {
        flex-direction: column;
        gap: 5px;
    }

    .dd-product-categories-menu li:not(:last-child) {
        border-right: none;
    }
}

.nav-links a, .dd-product-categories-menu a {
    padding: 0 5px;
    text-decoration: none;
    color: var(--text-gray) !important;
    font-family: 'Lato', sans-serif !important;
    font-size: 16px !important;
    font-weight: 300 !important;
}

header .current-menu-item > a, header .current-menu-parent > a{
    font-weight: 600 !important;
}

.nav-links a:hover, .dd-product-categories-menu a:hover {
    color: #878787;
}

.logo {
    flex-grow: 1;
    text-align: left;
}

.logo img {
    max-height: 50px;
    transition: all 0.3s ease;
}

.right-side {
    display: flex;
    align-items: center!important;
    justify-content: center !important;
    align-content: center !important;
    text-decoration: none;
    gap: 15px;
    margin-right: 25px;
}


.right-side a {
    text-decoration: none;
    color: var(--text-gray);
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    font-weight: 300;
    align-items: center !important;
}

.right-side a:hover {
    color: #000;
    text-decoration: underline;
}

.konto img {
    color: #a09f9f;
    align-content: center !important;
    align-items: center !important;
    width: 18px;
    height: 18px;
    margin-right: 5px; /* Odstęp między ikoną a napisem */
}

.konto a {
    align-items: center !important;
    align-content: center !important;
    text-decoration: none;
    color: var(--text-gray);
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    font-weight: 300;
}

.konto {
    display: flex;
    position: relative;
    font-size: 16px; /* Ustaw wielkość czcionki dla napisu Koszyk */
    cursor: pointer;
    justify-content: center !important;
    align-items: center !important;
    align-content: center !important;
}

.cart-trigger {
    display: flex;
    position: relative;
    font-size: 16px; /* Ustaw wielkość czcionki dla napisu Koszyk */
    cursor: pointer;
    justify-content: center;
    align-items: center !important;
    align-content: center !important;
}

.cart-trigger p {
    align-items: center !important;
    align-content: center !important;
    text-decoration: none;
    color: var(--text-gray);
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    font-weight: 300;
}

.cart-trigger p:hover {
    color: #000;
    text-decoration: underline;
}

.cart-trigger img {
    color: #a09f9f;
    align-content: center !important;
    align-items: center !important;
    width: 18px;
    height: 18px;
    margin-right: 5px; /* Odstęp między ikoną a napisem */
}

.mini-cart {
    position: absolute;
    top: 100%;
    right: 100px;;
    width: 350px;
    background-color: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.mini-cart ul.cart-items {
    list-style-type: none;
    margin: 0;
    padding: 0 15px 0 0;
    max-height: calc(50vh - 100px);
    overflow-y: scroll;
    scrollbar-width: thin;
    scrollbar-color: var(--text-gray) #f7f7f7;
}

.mini-cart .cart-item {
    margin-bottom: 20px;
}

.mini-cart .cart-item-header {
    display: flex;
    justify-content: center;
    align-items: start;
    gap: 15px;
}

.mini-cart .cart-item-image {
    min-width: 60px;
    max-width: 60px;
    display: flex;
    justify-content: center;
}

.mini-cart .cart-item-details {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.mini-cart  .cart-item-details .cart-item-column,
#dd-checkout-items-summary .cart-item-details .cart-item-column {
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    gap: 5px;
}

#dd-checkout-items-summary .cart-item-details a {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.mini-cart .cart-item-image img {
    width: 100%;
    height: auto;
    object-fit: contain;
    max-height: 60px;
}

.mini-cart .product-name {
    text-align: left !important;
    font-weight: 400 !important;
    display: block;
}

.mini-cart .product-name a {
    color: var(--text-gray) !important;
}

.mini-cart .product-sku {
    text-align: left !important;
    font-size: 12px;
    color: #999;
}

.mini-cart .product-attributes {
    font-size: 14px;
    color: #999;
}

.mini-cart .product-attributes b{
    font-weight: 600;
    color: #777777;
}

.mini-cart .cart-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.mini-cart .cart-quantity input {
    width: 50px;
    border-radius: 0 !important;
    border: 1px solid var(--primary-green);
    text-align: center;
}

.mini-cart .cart-price {
    display: flex;
    justify-content: flex-end;
    font-weight: 400;
}

.mini-cart .remove-item {
    color: var(--text-gray) !important;
    cursor: pointer;
}

.mini-cart .cart-remove:hover {
    color: var(--primary-green) !important;
    cursor: pointer;
}

.mini-cart .cart-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    font-weight: 400;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.mini-cart .cart-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
}

.mini-cart .cart-actions .checkout-button {
    margin-top: 10px;
    padding: 10px;
    background-color: var(--text-gray);
    color: #fff;
    text-align: center;
    text-decoration: none;
}

.mini-cart .cart-actions .update-cart {
    display: none;
}

.mini-cart .cart-actions .continue-shopping {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid var(--text-gray);
    background-color: transparent;
    color: var(--text-gray);
    text-align: center;
    text-decoration: none;
    font-size: 16px;
}

.mini-cart .cart-actions button {
    border: none;
    cursor: pointer;
}

.search-form {
    text-transform: lowercase !important;
}

.search-form i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
}

.search-form {
    display: flex;
    width: 250px;
    padding: 10px 0; /* Padding dla tekstu i ikony */
    border: 1px solid #ccc;
    border-radius: 0;
    font-size: 16px;
    font-family: 'Lato', sans-serif; /* FontAwesome dla ikony lupki */
    text-transform: lowercase !important;
    box-shadow: 0 0 3px #cacaca;
    align-items: center;
    align-content: center;
}

#search-input {
    border: none;
}

#search-button {
    left: 10px; /* Pozycjonowanie przy prawej krawędzi */
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
}

#search-input::placeholder {
    color: #999;
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    text-transform: lowercase !important;
}

/* Dodanie miejsca po lewej stronie dla ikony */
#search-input {
    padding-left: 30px; /* Miejsce na ikonę */
    border: none;
    outline: none;
}

#search-input:focus {
    border-color: transparent !important;
    border: none !important;
}

.search-form i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
}

@media (max-width: 960px) {
    .desktop-header, .header-block1, .header.block2, .nav-links, .logo, .right-side {
        display: none !important;
    }

    .mobile-header {
        margin: 0  !important;
        padding: 0 !important;
        display: block !important;
        position: fixed !important;
        top: 0 !important;
        z-index: 999 !important;
        width: 100%;
        background-color: #fff;
        border-bottom: 1px solid #eeeeee;
    }

    .dd-mobile-header-center-icons {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }

    .dd-mobile-header-center-icons a {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
    }

    .dd-mobile-header-center-icons img {
        height: 25px;
        width: 25px;
        color: #333 !important;
    }

       /* CSS dla mini koszyka */
    #mobile-mini-cart {
        position: absolute;
        top: 60px;
        left: 0;
        right: 0;
        width: auto !important;
        border-radius: 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        display: none;
        background-color: #fff;
        z-index: 1000;
        padding: 30px 20px 20px;
    }

    .mobile-site-header {
        background-color: #fff !important; /* Białe tło nagłówka */
        box-sizing: border-box;
    }
    .mobile-header-content {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 10px;
    }

    .mobile-logo {
        flex: 1 !important;
        display: flex !important;
        justify-content: flex-start !important;
        margin-right: 20px;
    }

    .mobile-logo img {
        max-width: 120px !important;
        height: auto;
    }

    .mobile-center-content {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        flex: 2 !important;

    }

    .mobile-cart-trigger, .mobile-right-side {
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        font-size: 24px !important;
        color: #333 !important;
        text-decoration: none !important;
        position: relative !important;
    }

    .menu-toggle {
        flex: 1 !important;
        display: flex !important;
        justify-content: flex-end !important;
    }

    .menu-icon {
        margin-right: 10px;
        font-size: 26px !important;
        cursor: pointer !important;
    }

    /* Menu początkowo ukryte */
    .hidden {
        display: none !important;
    }

    .mobile-menu {
        background-color: #fff;
        padding: 20px;
        list-style: none !important;
        text-align: center;
        max-height: 90vh;
        overflow-y: auto;
    }

    .mobile-menu ul {
        list-style: none !important;
        margin: 0;
        padding: 0;
    }

    .mobile-menu li {
        list-style: none !important;
        margin: 15px 0;
    }

    .mobile-menu a {
        text-decoration: none !important;
        color: #333;
        font-size: 18px;
    }

    /* Wyświetlanie menu po kliknięciu */
    .menu-open .mobile-menu {
        display: block !important;
    }
}

.site-footer {
    border-top: 1px solid #EEEEEEEE;
    display: flex;
    padding: 50px !important;
}

.site-footer .title {
    font-family: 'Lato', sans-serif;
    font-weight: bold;
    color: var(--text-gray);
    font-size: 16px;
}

.site-footer p {
    font-family: 'Lato', sans-serif;
    color: var(--text-gray-light);
    font-weight: 300;
    margin-bottom: 15px;
}

.footerbox1 {
    display: flex;
    flex-wrap: wrap;
    gap: 35px;
    justify-content: space-between;
    width: 100%;
}

.footerbox1 .footer2 ul li {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.footerbox1 .footer2 ul #menu-item-2215 {
    max-width: 140px;
}

.footerbox1 i {
    font-size: 40px;
    color: var(--text-gray);
}

.footer1 {
    justify-content: center;
    text-align: center;
}

.footer2 ul {
    list-style: none;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0;
}

.footer2 li a {
    text-decoration: none;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray-light);
}

.social-icons {
    justify-content: center;
    display: flex;
    gap: 25px;
}

.footer-newsletter {
    width: 400px;
    border: 1px solid #bbb;
    justify-content: center;
    align-items: center;
    align-content: center;
    text-align: center;
    padding: 25px 50px;
}

.footer-newsletter .newsletter-discount {
    letter-spacing: 0;
    margin-top: 0;
    margin-bottom: 0;
    color: #4A6A65;
    font-size: 1.5em;
    font-weight: bold;
}

.footer-newsletter h3 {
    letter-spacing: 0;
    font-weight: 300;
}

.email-subscription {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    font-family: 'Lato', sans-serif;
}

.email-subscription label {
    text-align: left!important;
    margin-bottom: 10px;
    font-size: 12px;
    color: #777676;
}

.input-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.input-group input[type="email"] {
    flex-grow: 1;
    padding: 10px;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 0 !important;
    outline: none;
    transition: border-color 0.3s;
}

.input-group input[type="email"]:focus {
    border-color: #007BFF;
}

.subscribe-button {
    padding: 10px 20px;
    font-size: 14px;
    color: var(--text-gray);
    background-color: transparent;
    border: 1px solid var(--text-gray);
    cursor: pointer;
    transition: background-color 0.3s;
    width: fit-content;
}

.subscribe-button:hover {
    background-color: #A9ABAC;
    color: white;
}

.mobile-site-footer {
    display: none !important;
}


/* Mobilne wersje dla sekcji */
@media (max-width: 768px) {
    .input-group {
        flex-direction: column;
        align-items: center;
    }

    .site-footer {
        display: none !important;
    }

    .mobile-site-footer {
        display: block !important;
        margin: 25px 0;
        padding: 10px;
    }

    .-mobile-site-footer h2 {
        font-family: 'Lato', sans-serif;
        letter-spacing: 5px;
        color: var(--text-gray);
    }

    .mobile-email-subscription label {
        text-align: left!important;
        margin-bottom: 10px;
        font-size: 12px;
        color: #777676;
    }

    .mobile-site-footer .title {
        font-family: 'Lato', sans-serif;
        font-size: 1.17em;
        font-weight: bold;
        color: var(--text-gray);
    }

    .mobile-social-icons a {
        color: unset;
        text-decoration: none;
    }

    .mobile-site-footer p {
        font-family: 'Lato', sans-serif;
        color: #bbb;
        margin-bottom: 15px;
    }

    .mobile-footer-newsletter {
        margin: 25px auto;
        text-align: center;
        padding: 20px;
        background-color: white;
        border: 1px solid #ccc;
        width: 100%;
    }

    .mobile-footer-newsletter .newsletter-discount {
        font-family: 'Lato', sans-serif;
        letter-spacing: 0;
        margin-top: 0;
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 0;
        color: #4A6A65;
    }

    .mobile-footer-newsletter .footer-title {
        letter-spacing: 0;
        font-weight: 300;
        font-size: 1.17em;
        color: var(--text-gray);
    }

    .mobile-email-subscription {
        margin-top: 20px;
    }

    .mobile-input-group {
        margin: 25px 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .mobile-input-group input {
        margin: 5px auto;
        width: 50%;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 0;
    }

    .mobile-subscribe-button {
        margin: 5px auto;
        width: 50%;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: transparent;
        color: #333;
        cursor: pointer;
        border-radius: 0;
    }

    .mobile-subscribe-button:hover {
        background-color: #333;
        color: white;
    }

    .mobile-footer-box {
        margin: 25px 0;
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .mobile-footerbox1 {
        margin: 25px 0;
        padding: 0;
        display: flex;
        align-items: left;
        align-content: flex-start;
    }
    .mobile-footer1 {
        margin: 25px 0;
        padding: 0;
        display: flex;
        align-content: center !important;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    .mobile-footer1 img {
        align-content: center !important;
    }

    .mobile-footer2 {
        width: 50%;
        flex: 1;
        margin: 25px 0;
        padding: 0;
        text-align: center;
    }

    .mobile-footer2 ul {
        padding: 0;
        list-style: none;
        margin: 0;
    }

    .mobile-footer2 li {
        margin-bottom: 10px;
    }

    .mobile-footer2 a {
        color: var(--text-gray);
        text-decoration: none;
    }

    .mobile-footer2 a:hover {
        text-decoration: underline;
    }

    .mobile-footer-info {
        text-align: center;
        height: max-content !important;
    }

    .mobile-social-icons {
        font-size: 24px;
        margin-top: 10px;
    }

    .mobile-social-icons i {
        margin: 0 10px;
    }
}

.error-section {
    align-items: center;
    text-align: center;
    align-content: center;
    padding: 30px 0;
}

.error-section h2 {
    font-size: 72px;
    font-family: 'Lato', sans-serif;
    letter-spacing: 10px;
    color: #101111d3;
}

.error-section p {
    font-size: 24px;
    font-family: 'Lato', sans-serif;
    color: black;
}

.error-button {
    padding: 15px 50px;
    align-items: center;
    justify-content: center;
    background-color: var(--accent-red);
    border: none;
}

.error-button:hover {
    padding: 15px 50px;
    align-items: center;
    justify-content: center;
    background-color: #e07a6b;
    color: white;
    border: none;
    transform: scale(1.05);
}

.error-button a {
    font-size: 16px;
    font-family: 'Lato', sans-serif;
    color: white;
    text-decoration: none;
}


@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}


.main-box {
    position: absolute;
    bottom: 30px;
    right: 80px;
    text-align: left;
    color: #fff;
    z-index: 1;
}

.main-box h2 {
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    letter-spacing: 5px;
    line-height: 24px;
    font-size: 16px;
    margin: 0 0 10px 0;
}

.main-box h3 {
    font-family: 'FreightBigProLight', serif;
    letter-spacing: 5px;
    line-height: 48px;
    font-size: 24px;
    margin: 0 0 20px 0;
}

.main-button {
    cursor: pointer;
    font-family: 'Lato', sans-serif;
    display: inline-block;
    padding: 15px 75px;
    background-color: transparent;
    color: #fff;
    text-decoration: none;
    border: 1px solid white;
    font-size: 16px;
}

.main-button:hover {
    cursor: pointer;
    background-color: white;
    border: none;
    color: black;
}

@media (max-width: 768px) {
    .main-box {
        bottom: 15px;
        left: 20px;
        text-align: left; /* Wyśrodkowanie tekstu */
    }

    .main-box h2, .main-box h3 {
        letter-spacing: 5px; /* Normalne odstępy na mobile */
        font-size: 14px; /* Mniejszy rozmiar czcionki */
        line-height: 22px;
        margin: 0; /* Usuń marginesy */
    }

    .main-box h3 {
        font-size: 18px;
        line-height: 32px;
    }

    .main-button {
        padding: 10px 30px; /* Zmniejsz padding dla przycisku */
        font-size: 14px;
    }
}

.naglowek-nowosci h2 {
    margin-top: 50px;
    margin-bottom: 40px;
    justify-content: center;
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    letter-spacing: 5px;
}

.nowosci-produkty {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;
    list-style: none;
    padding: 0;
    margin: 0 auto;
    max-width: 1200px;
    text-decoration: none;
}

.nowosci-produkty .product {
    text-align: center;
    position: relative;
    padding: 15px;
    background-color: white;
    text-decoration: none;
}


.nowosci-produkty .product .product-image {
    overflow: hidden;
    margin-bottom: 10px;
}

.nowosci-produkty .product .product-image img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.nowosci-produkty .product .product-image:hover img {
    transform: scale(1.1);
}

.dd-product-image {
    overflow: hidden;
    height: fit-content;
    margin-bottom: 20px;
    position: relative;
}

.dd-product-image-labels {
    position: absolute;
    z-index: 100;
    top: 30px;
    left: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dd-product-image-labels div {
    padding: 3px 15px;
    color: white;
    font-size: 12px;
    text-transform: uppercase;
    width: 110px;
    text-align: center;
}

.dd-product-on-sale-label {
    background: var(--accent-red);
}

.dd-product-new-label {
    background: var(--primary-green);
}

.dd-product-image img {
    margin: 0 !important;
}

.dd-product-image img:hover {
    transform: scale(1.07);
}

.nowosci-produkty .product .product-name {
    font-size: 16px;
    font-family: 'Lato', sans-serif;
    letter-spacing: 0;
    text-transform: none;
    color: var(--text-gray);
    font-weight: 400;
    text-decoration: none;
    margin: 10px 0;
}
.nowosci-produkty .product a {
    text-decoration: none; /* Usuń podkreślenie z całego linku */
}

.nowosci-produkty .product a:hover .product-name,
.nowosci-produkty .product a:hover .price {
    text-decoration: none; /* Usuń podkreślenie także podczas hover */
}

.nowosci-produkty .product .price {
    font-family: 'Lato', sans-serif;
    text-decoration: none;
    font-size: 14px !important;
    color: var(--text-gray);
    font-weight: 300;
}


@media (max-width: 768px) {
    .nowosci-produkty {
        grid-template-columns: repeat(2, 1fr); /* Dwie kolumny dla tabletów */
        margin: 0 !important;
        padding: 0 10px !important;
        grid-gap: 10px !important;
        width: 100%;
    }

    .nowosci-produkty .product .product-image img {
        width: 100% !important;
        height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        transition: none;
    }

    .nowosci-produkty .product {
        width: 100%;
        text-align: center;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
        background-color: white;
        text-decoration: none;
    }
}


.button-nowosci {
    display: flex;
    justify-content: center; /* Wyśrodkowanie przycisku w poziomie */
    align-items: center; /* Wyśrodkowanie w pionie, jeśli to wymagane */
    margin: 25px auto; /* Centrowanie w kontenerze */
    text-align: center;
}

.dd-black-border-button {
    padding: 15px 75px;
    background-color: transparent;
    border: 1px solid #3b3b3b;
    font-size: 16px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: #3b3b3b;
    cursor: pointer; /* Kursor zmienia się w rączkę przy najechaniu */
    transition: background-color 0.2s ease, color 0.2s ease; /* Animacja przycisku */
    text-decoration: none;
}


.dd-black-border-button:hover {
    justify-content: center;
    text-align: center;
    padding: 15px 75px;
    background-color: #3b3b3b;
    border: 1px solid #3b3b3b;
    font-size: 16px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: white;
}

.front-boxy {
    margin: 70px auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
}

@media (max-width: 991px) {
    .front-boxy {
        grid-template-columns: 1fr;
    }

    .dd-frontbox {
        padding: 25px !important;
    }
}

.dd-frontbox {
    padding: 50px;
    color: var(--text-gray);
    font-size: 14px;
    font-weight: 300;
    font-family: 'Lato', sans-serif;
    line-height: 24px;
    display: flex;
    flex-direction: column;
    flex-basis: calc(33.33% - 25px);
}

.dd-frontbox h2 {
    font-size: 20px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    letter-spacing: 5px;
    text-align: left;
}

.dd-frontbox a {
    align-self: center;
    margin-top: auto;
}

.frontbox1 {
    border: 1px solid #C15C37;
}

.frontbox1 h2 {
    color: #C15C37;
}

.frontbox1 button {
    border: 1px solid #C15C37;
    color: #C15C37;
}

.frontbox2 {
    border: 1px solid #4A6C83;
}

.frontbox2 h2 {
    color: #4A6C83;
}

.frontbox2 button {
    border: 1px solid #4A6C83;
    color: #4A6C83;
}

.frontbox3 {
    border: 1px solid #A9ABAC;
}

.frontbox3 h2 {
    color: #A9ABAC;
}

.frontbox3 button {
    border: 1px solid #A9ABAC;
    color: #A9ABAC;
}

.opinie-container {
    display: flex;
    gap: 15px !important;
}

.opinia {
    display: flex !important;
    flex-direction: column;
    justify-content: space-between;
    gap: 15px;
    margin: 10px;
    width: 250px;
    background-color: white;
    align-content: center;
    align-items: center;
    text-align: center;
    padding: 45px 30px;
}

.dd-opinion-signature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Lato', sans-serif;
}

.dd-opinion-signature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    font-family: 'Lato', sans-serif;
    text-align: left;
}

.dd-opinion-signature img {
    height: 50px;
    width: 50px;
    border-radius: 50%;
}

.opinia .dd-opinion-content {
    text-align: left !important;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    font-size: 16px;
    line-height: 24px;
    font-weight: 300;
}

.opinia img {
    width: auto;
    max-height: 75px;
    justify-content: center;
    text-align: center;
    align-content: center;
}

.button-opinie {
    display: flex;
    justify-content: center; /* Wyśrodkowanie przycisku w poziomie */
    align-items: center; /* Wyśrodkowanie w pionie, jeśli to wymagane */
    margin: 50px auto 0; /* Centrowanie w kontenerze */
    text-align: center;
}

@media (max-width: 768px) {
    .opinie-section {
        margin: 25px 0 0 !important;
        padding: 40px 0;
        height: auto; /* Dostosowanie wysokości do zawartości */
    }

    .opinie-container {
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column; /* Ustawienie opinii w kolumnie */
        gap: 15px;
    }

    .opinia {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 15px;
        padding: 30px
    }

    .button-opinie {
        margin-top: 30px; /* Mniejszy margines dla mobilnych ekranów */
    }
}

.rokitnik {
    background-color: var(--accent-red);
}

.rokitnik p {
    max-width: 1350px;
    margin: 0 auto;
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    font-weight: 300;
    line-height: 28px;
    font-style: normal;
}

@media (max-width: 768px) {
    .rokitnik p {
        font-size: 16px; /* Mniejszy rozmiar czcionki dla paragrafu */
        line-height: 24px; /* Mniejsze odstępy między liniami tekstu */
    }
}

.product-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    padding: 60px 0;
}

.product-image-large {
    display: flex;
    flex: 1;
    margin-right: 40px;
    justify-content: center;
    align-items: center;
    align-content: center;
}

.product-image-large img {
    width: auto;
    height: 500px;
}

.product-details {
    flex: 1;
    text-align: left;
    display: flex;
    flex-direction: column;
}

.home .product-details {
    gap: 20px;
}

.product-name-main {
    font-family: 'Lato', sans-serif;
    font-weight: 300;
    color: var(--text-gray);
    font-size: 24px;
    letter-spacing: 5px;
    text-transform: uppercase;
    margin-bottom: 15px;
}

.short-description {
    font-family: 'Lato', sans-serif;
    line-height: 30px;
    font-weight: 300;
    font-size: 16px;
    margin-bottom: 20px;
}

.price {
    display: flex;
    justify-content: center;
    gap: 5px;
    font-family: 'Lato', sans-serif !important;
    font-size: 16px !important;
    font-weight: 300 !important;
    color: var(--text-gray) !important;
    height: 20px;
    margin-bottom: 30px; /* Większy odstęp od ceny */
}

.check-product-button {
    display: inline-block;
    width: 150px;
    padding: 15px 30px;
    background-color: transparent;
    color: #E0B6AF;
    text-decoration: none;
    text-align: center;
    align-content: center;
    font-size: 16px;
    border: 1px solid #E0B6AF;
    transition: background-color 0.3s ease;
    margin-top: 10px; /* Odstęp między ceną a przyciskiem */
}

.check-product-button:hover {
    background-color: #E0B6AF;
    color: white;
}

.check-product-button1 {
    display: inline-block;
    width: 150px;
    padding: 15px 30px;
    background-color: transparent;
    color: #B7C4C7;
    text-decoration: none;
    text-align: center;
    align-content: center;
    font-size: 16px;
    border: 1px solid #B7C4C7;
    transition: background-color 0.3s ease;
    margin-top: 10px; /* Odstęp między ceną a przyciskiem */
}

.check-product-button1:hover {
    background-color: #B7C4C7;
    color: white;
}

@media (max-width: 768px) {
    .product-container {
        flex-direction: column; /* Ustawienie kontenera w kolumnie */
        align-items: center; /* Wyśrodkowanie elementów w poziomie */
        margin: 25px 5px !important;
        padding: 0;/* Mniejsze paddingi */
    }

    .product-image-large {
        margin: 0; /* Usunięcie marginesu po prawej stronie */
        margin-bottom: 20px; /* Odstęp między zdjęciem a opisem */
        justify-content: center;
        align-items: center;
    }

    .product-image-large img {
        width: 100%; /* Dostosowanie szerokości zdjęcia do szerokości kontenera */
        height: auto; /* Automatyczna wysokość, proporcjonalna do szerokości */
    }

    .product-name-main {
        font-size: 20px; /* Mniejszy rozmiar czcionki dla nagłówka */
        letter-spacing: 3px; /* Mniejsze odstępy między literami */
    }

    .short-description {
        font-size: 14px; /* Mniejszy rozmiar czcionki dla opisu */
        line-height: 24px; /* Mniejsze odstępy między liniami */
        margin-bottom: 15px; /* Mniejsze marginesy poniżej opisu */
    }

    .price-main {
        font-size: 16px; /* Mniejszy rozmiar czcionki dla ceny */
        margin-bottom: 20px; /* Mniejsze marginesy poniżej ceny */
    }

    .check-product-button,
    .check-product-button1,
    .check-product-button2 {
        width: 90%; /* Przycisk zajmuje pełną szerokość kontenera */
        padding: 12px 0; /* Mniejsze paddingi dla przycisków */
        margin-top: 10px; /* Mniejsze marginesy powyżej przycisków */
        margin-left: 10px;
        margin-right: 10px;
    }
}

.marula {
    background-color: #4A6A65;
}

.marula p {
    max-width: 1350px;
    margin: 0 auto;
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    font-weight: 300;
    line-height: 28px;
    font-style: normal;
}

@media (max-width: 768px) {
    .marula p {
        font-size: 16px; /* Mniejszy rozmiar czcionki dla paragrafu */
        line-height: 24px; /* Mniejsze odstępy między liniami tekstu */
    }
}

.kwas {
    background-color: #A9ABAC;
}

.dd-main-page-product-desc {
    color: white;
    justify-content: center;
    text-align: center;
    align-content: center;
    padding: 80px 20px;
}

.dd-main-page-product-desc h2 {
    margin: 25px auto;
    font-family: 'FreightBigProLight', serif;
    display: inline-block;
    transform: scale(1, 1.5);
    font-weight: 300;
    font-size: 24px;
    letter-spacing: 5px;
}

@media (max-width: 768px) {
    .dd-main-page-product-desc {
        padding: 45px 0;
        text-align: center;
    }

    .dd-main-page-product-desc h2 {
        font-size: 24px;
        letter-spacing: 2px;
        transform: scale(1, 1.2);
    }
}

.kwas p {
    max-width: 1350px;
    margin: 0 auto;
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    font-weight: 300;
    line-height: 28px;
    font-style: normal;
}

@media (max-width: 768px) {
    .kwas p {
        font-size: 16px; /* Mniejszy rozmiar czcionki dla paragrafu */
        line-height: 24px; /* Mniejsze odstępy między liniami tekstu */
    }
}

.laka {
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('../../public/laka.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    justify-content: center;
    text-align: center;
    align-content: center;
    padding: 80px 0;
}

.laka h2 {
    margin: 25px auto !important;
    font-family: "freight-big-pro-light", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 46px;
    line-height: 72px;
    letter-spacing: -0.01em;
}

.laka p {
    max-width: 1350px;
    margin: 0 auto;
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    font-weight: 300;
    line-height: 28px;
    font-style: normal;
}

@media (max-width: 768px) {
    .laka h2 {
        font-size: 24px; /* Mniejszy rozmiar czcionki dla nagłówka */
        letter-spacing: 2px; /* Mniejsze odstępy między literami */
        transform: scale(1, 1.2); /* Dostosowanie skalowania tekstu */
    }

    .laka p {
        font-size: 16px; /* Mniejszy rozmiar czcionki dla paragrafu */
        line-height: 24px; /* Mniejsze odstępy między liniami tekstu */
    }
}

.wpisy-section h2 {
    margin-top: 50px;
    justify-content: center;
    text-align: center;
    font-size: 40px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
}

/* Stylizacja sekcji blog-posts */
.blog-posts {
    width: 100%;
    margin: 25px 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
}

/* Stylizacja każdego wpisu */
.blog-post {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
    position: relative;
    height: 100%;
}

/* Stylizacja obrazka wpisu */
.post-thumbnail {
    overflow: hidden;
    height: 250px;
    display: flex;
}

.post-thumbnail a {
    width: 100%;
    height: 100%;
    display: flex;
}

.post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.post-thumbnail:hover img {
    transform: scale(1.05); /* Powiększa obrazek przy najechaniu */
}

.meta {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
}

.post-title {
    font-size: 16px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    margin: 10px 0;
    line-height: 1.4;
}

.post-title a {
    text-decoration: none;
    color: inherit;
}

.post-title a:hover {
    text-decoration: underline;
}

.post-info h3 a {
    text-decoration: none;
    color: #000;
}

.post-date {
    font-size: 14px;
    color: #888;
    margin: 5px 0;
}

.read-more {
    font-size: 14px;
    color: var(--text-gray);
    text-decoration: none;
    margin-top: 10px;
    align-self: flex-start;
}

.read-more:hover {
    text-decoration: underline;
}



@media (max-width: 991px) {
    .blog-posts {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin: 20px 0;
    }

    .post-thumbnail {
        height: 200px;
    }
}



@media (max-width: 768px) {
    .blog-posts {
        grid-template-columns: repeat(1, 1fr);
        gap: 20px;
    }

    .post-thumbnail {
        height: 250px;
    }
}

.insta-section {
    width: 100%;
    max-width: 1250px;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 50px auto;
}

.insta-section h2 {
    margin-top: 50px;
    justify-content: center;
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    letter-spacing: 5px;
}

.insta-section p {
    justify-content: center;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
}

.insta-section img {
    width: 800px;
    height: auto;
}

.insta-section i {
    font-size: 30px;
}

@media (max-width: 768px) {
    .insta-section {
        width: 100%;
        max-width: 100%; /* Ustawienie maksymalnej szerokości na 100% */
        padding: 0; /* Dodanie paddingu dla lepszej przestrzeni */
        margin: 20px 0; /* Mniejsze marginesy */
        text-align: center; /* Wyśrodkowanie tekstu */
    }

    .insta-section h2 {
        margin-top: 20px; /* Mniejsze marginesy górne */
        font-size: 18px; /* Mniejszy rozmiar czcionki */
        letter-spacing: 3px; /* Mniejsze odstępy liter */
    }

    .insta-section p {
        font-size: 12px; /* Mniejszy rozmiar czcionki */
        line-height: 1.5; /* Większa przestrzeń między wierszami */
    }

    .insta-section img {
        width: 100%; /* Obrazek zajmuje 100% szerokości kontenera */
        height: auto; /* Automatyczna wysokość */
    }

    .insta-section i {
        font-size: 24px; /* Mniejszy rozmiar ikony */
    }

}

.page-numbers {
    color: black;
}

.partnerzy {
    border-top: 1px solid #EEEEEEEE;
    align-content: center !important;
    align-items: center !important;
    margin-bottom: 50px;
}

.partnerzy .title {
    margin: 35px auto 25px;
    justify-content: center;
    text-align: center;
    font-weight: 400;
    font-family: 'Lato', sans-serif;
    font-style: normal;
    font-size: 32px;
    line-height: 155%;
    color: #000000;
}

.partnerzy-img {
    display: flex !important;
    justify-content: center;
    align-content: center !important;
    align-items: center !important;
}

.partnerzy-img img {
    width: 150px;
    height: auto;
    align-content: center !important;
    align-items: center !important;
    margin: 15px;
}

.partnerzy-slider {
    top: auto !important;
    margin: 0 auto !important;
    align-content: center !important;
    align-items: center !important;
}

@media (max-width: 768px) {
    .partnerzy-slider {
        width: 100%; /* Dopasowanie slidera do szerokości ekranu */
        margin: 0 auto;
    }

    .partnerzy-img {
        display: flex;
        justify-content: space-around; /* Równe odstępy między obrazami */
        align-items: center;
        width: 100%; /* Kontener na całą szerokość */
        padding: 0 10px;
    }

    .partnerzy-img img {
        width: 45%; /* Dopasowanie szerokości obrazów, aby zmieściły się dwa obok siebie */
        margin: 0;  /* Usuń marginesy, aby były równo rozmieszczone */
    }
}

.promo-new, .categories {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
}

.promo-new h3, .categories h3 {
    margin: 0 15px;
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    font-weight: 300;
}

.promo-new a, .categories a {
    text-decoration: none;
    color: var(--text-gray);
}

.sep {
    height: 30px;
    width: 2px;
    background-color: #A9ABAC;
}

.woocommerce-ordering select {
    font-size: 16px;
    color: #555;
    padding: 8px;
    font-size: 16px;
    border: 1px solid #ddd;
    cursor: pointer;
    /* Dodatkowe stylizacje, jeśli potrzebne */
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* Wyświetl produkty w 4 kolumnach */
    gap: 20px;
    text-align: center;
}

.product-card a {
    text-decoration: none;
    color: var(--text-gray);
}
.product-name {
    text-align: center;
    font-size: 16px;
    font-family: 'Lato', sans-serif;
    color: var(--text-gray);
    font-weight: 300;
    letter-spacing: 0;
    text-transform: none;
}

.product-image img {
    transition: transform 0.3s ease;
}

.product-image img:hover {
    transform: scale(1.05); /* Powiększenie obrazu po najechaniu */
}

@media (max-width: 768px) {
    .promo-new, .categories {
        flex-direction: column; /* Zmieniamy na kolumny dla urządzeń mobilnych */
        margin-bottom: 15px;
        align-items: flex-start !important;
        justify-content: flex-start !important;
        text-align: left !important;
    }

    .promo-new h3, .categories h3 {
        margin: 10px 0; /* Zmniejszamy margines dla mobilnych */
        font-size: 16px;
    }

    .sep {
        display: none; /* Ukrywamy separator dla urządzeń mobilnych */
    }

    .sortowanie-container {
        flex-direction: column; /* Sortowanie w jednej kolumnie */
        text-align: center; /* Wyśrodkowanie */
        margin: 15px 0; /* Mniejszy margines */
    }

    .all-products-section h2 {
        font-size: 20px;
        margin-left: 0;
        text-align: center; /* Wyśrodkowujemy nagłówek */
    }

    .product-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 produkty na wiersz dla mobilnych */
        gap: 10px; /* Zmniejszamy odstępy między produktami */
    }

    .product-name {
        font-size: 14px; /* Zmniejszamy rozmiar czcionki dla nazw produktów */
    }

    .sort-dropdown {
        width: 100%; /* Dropdown zajmuje całą szerokość */
        margin-top: 10px;
    }
}


/* Wrapper dla strony produktu */
.product-page {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.single-product-wrapper {
    display: flex; /* Używamy flexboxa do rozmieszczenia kolumn */
    gap: 20px; /* Odstęp między kolumnami */
    padding: 20px; /* Odstęp wewnętrzny */
}

.product-gallery,
.product-details {
    flex: 1; /* Obie sekcje zajmują równą ilość miejsca */
}

.product-gallery {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.product-gallery .product-thumbnails {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    padding: 0 !important;
    min-width: 110px;
    max-height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
}

.product-gallery .product-thumbnails img {
    cursor: pointer;
    margin-bottom: 0 !important; /* Odstęp między miniaturkami */
    width: 100px; /* Szerokość miniaturki */
    height: 100px; /* Automatyczna wysokość w stosunku do szerokości */
    padding: 0 !important;
    border: 2px solid white;
}

.product-gallery .product-images {
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-gallery .product-images img {
    width: auto;
    max-height: 600px;
}

.dd-thumbnail-active {
    border-color: var(--primary-green) !important;
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
}

.product-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.product-details h1.product_title {
    font-family: 'Lato', sans-serif;
    font-size: 28px;
    letter-spacing: 5px;
    color: var(--text-gray);
    font-weight: 400 !important;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.product-details .price {
    font-size: 22px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

/* Stylizacja ceny */
.product-price {
    font-size: 16px !important;
    font-weight: bold;
    font-family: 'Lato', sans-serif !important;
    color: var(--text-gray) !important;
    text-decoration: none !important;
}

.product-link  .woocommerce-Price-amount {
    display: flex;
    flex-direction: column;
    font-size: 16px !important;
    font-weight: 600;
    margin-bottom: 20px;
    font-family: 'Lato', sans-serif !important;
    color: var(--text-gray) !important;
    text-decoration: none !important;
}

.price ins, .product-price ins {
    text-decoration: none !important;
}

.product-price del .woocommerce-Price-amount {
    color: var(--text-gray-light) !important;
    font-weight: 300;
}


.product-price del {
    text-decoration-color: var(--text-gray-light) ;
}

.custom-field {
    color: var(--text-gray);
    font-weight: bold;
}

.sku {
    font-weight: 300;
    color: var(--text-gray);
}

.woocommerce-product-details__short-description {
    font-weight: 300;
    color: var(--text-gray);
}
/* Stylizacja dla ilości i przycisków plus-minus */
.quantity {
    display: inline-flex;
    align-items: center;
    border: 1px solid #dfdfdf;
    padding: 0 10px;
}

.quantity button {
    background-color: #f1f1f1;
    color: black;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    height: 30px;
    width: 30px;
}

.quantity input.qty {
    border: none;
    text-align: center;
    width: 20px;
    height: 45px;
    margin: 0 5px;
}

/* Usunięcie strzałek w polu liczbowym */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Usunięcie strzałek w Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

/* Stylizacja przycisku "Dodaj do koszyka" */
 .product form.cart .single_add_to_cart_button {
    background-color: #646E69 !important;
    border: none;
    color: white;
    padding: 16px 50px;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border-radius: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.woocommerce .product form.cart .single_add_to_cart_button::after {
    top: auto;
}

.woocommerce .product form.cart .single_add_to_cart_button:hover {
    background-color: #555 !important;
}

.product-link a {
    text-decoration: none !important;
}


.product-accordion {
    border-collapse: collapse;
    margin-bottom: 30px;
}

.product-accordion h2.accordion-toggle {
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    color: #787878;
    font-weight: 400;
    font-size: 16px;
    cursor: pointer;
    background-color: white;
    padding: 25px 40px;
    border-bottom: 1px solid #dbdbdb;
}

.accordion-content {
    display: none;
}

.accordion-toggle {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    margin-bottom: 10px;
    padding: 10px 0;
}

.accordion-toggle .accordion-icon {
    transition: transform 0.3s ease;
}

/* Rotate the icon when active */
.accordion-toggle .accordion-icon i {
    transition: .2s ease-in-out;
}

.accordion-toggle.active .accordion-icon i {
    transform: rotate(180deg);
    transition: .2s ease-in-out;
}

.accordion-content.active {
    display: block;
}

@media (max-width: 768px) {
    .product-gallery .product-thumbnails {
        flex-direction: row;
        height: auto;
        overflow-x: auto;
    }

    .product-gallery {
        flex-direction: column-reverse;
    }

    .product-gallery .product-images {
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .product-gallery .product-images img {
        max-height: 300px;
    }

    .product-details {
        width: 100%;
        margin-top: 20px;
        padding: 0;
    }

    .product-details h1.product_title {
        font-size: 24px;
        margin-bottom: 10px;
        text-align: left;
    }

    .custom-field, .sku {
        text-align: left;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .woocommerce-product-details__short-description {
        font-size: 14px;
    }

    .quantity button {
        background-color: white;
        padding: 0;
        font-size: 14px !important;
        cursor: pointer;
        height: 45px;
        width: 20px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
    }

    .quantity input.qty {
        border: none;
        text-align: center;
        width: 40px !important;
        height: 45px;
    }
    .quantity {
        width: 35%; /* Ilość zajmuje 33% szerokości */
        display: flex; /* Wyświetlaj elementy w jednym rzędzie */
        align-items: center; /* Wyrównaj elementy w pionie */
        justify-content: flex-start !important; /* Wyrównaj ilość do lewej */
        border: 1px solid rgb(136, 136, 136);
        margin-right: 0; /* Usuń wszelkie marginesy */
    }

    .woocommerce .product form.cart .single_add_to_cart_button {
        width: 100%; /* Przycisk zajmuje resztę szerokości */
        display: flex;
        align-items: center; /* Wyrównanie przycisku w pionie */
        justify-content: center; /* Wyśrodkuj treść przycisku */
        padding: 16px 45px; /* Zmniejsz padding w poziomie */
        background-color: #646E69 !important;
        color: white;
        cursor: pointer;
        border: none;
        transition: background-color 0.3s ease;
        border-radius: 0;
    }

    .quantity-and-cart {
        display: flex; /* Wyświetl ilość i przycisk obok siebie */
        justify-content: space-between; /* Rozdziel ilość i przycisk */
        gap: 10px;
        align-items: center; /* Wyrównanie elementów w pionie */
        width: 100%; /* Całość zajmuje 100% szerokości kontenera */
        padding: 0; /* Usuń odstępy wokół */
        margin: 0; /* Usuń marginesy */
    }


    /* Stylizacja akordeonów */
    .product-accordion h2.accordion-toggle {
        font-size: 16px; /* Zmniejsz czcionkę nagłówka akordeonu */
        padding: 20px; /* Zmniejsz padding */
        margin-left: 0;
        margin-right: 0;
        text-align: center;
    }

    .accordion-content {
        padding: 20px; /* Zmniejsz padding w treści akordeonu */
        margin-left: 0;
        margin-right: 0;
    }
}

.categorie-title {
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    letter-spacing: 5px;
    text-transform: uppercase;
    font-size: 20px;
    margin-bottom: 10px;
}

.categorie-description {
    font-size: 14px;
    color: #555;
}

.categorie-products {
    margin-top: 20px;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4 kolumny */
    gap: 20px; /* Odstęp między produktami */
    text-decoration: none !important;
}

.product-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    text-decoration: none !important;
}

.product-item img {
    width: 100%;
    height: 300px !important; /* Stała wysokość dla zdjęć */
    object-fit: contain;
}

/* @media screen and (max-width: 960px) {
    .product-item img {
        height: auto !important;
    }
} */

.product-link {
    text-decoration: none !important;
}
.product-title  {
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    margin: 10px 0;
    font-weight: 400;
    color: #333;
    text-decoration: none !important;
}

.product-title a {
    color: #333;
    text-decoration: none !important;
}
.product-price {
    font-family: 'Lato', sans-serif;
    font-size: 16px;
    color: #333;
    text-decoration: none !important;
}

.product-price a {
    color: #333;
    text-decoration: none !important;
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 kolumny na mniejszych ekranach */
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr; /* 1 kolumna na bardzo małych ekranach */
    }
}

.single-post {
    font-family: 'Lato', sans-serif;
}

@media (max-width: 768px) {
    .single-post {
        margin-top: 0;
        font-family: 'Lato', sans-serif;
    }
}
/* Tytuł wpisu */
.entry-title1 {
    font-family: 'Lato', sans-serif !important;
    font-size: 36px;
    font-weight: 400;
    text-align: left;
    margin-bottom: 20px;
    letter-spacing: 5px;
    text-transform: uppercase;
    color: var(--text-gray);
}

/* Data wpisu */
.entry-date {
    font-size: 16px;
    color: #888;
    text-align: left;
    margin-bottom: 20px;
}

/* Obraz wyróżniający */
.featured-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto 20px auto; /* Obraz wyróżniający na środku */
}

/* Treść wpisu */
.entry-content {
    line-height: 1.6;
    margin-bottom: 40px;
    color: #333;
}

/* Odstępy między akapitami w treści */
.entry-content p {
    margin-bottom: 20px !important;
}

.entry-content h2:not(.product-title) {
    margin-top: 25px;
    margin-bottom: 25px !important;
}

.entry-content img {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
}

.blog-section {
    padding-bottom: 30px !important;
}

.page-title {
    font-size: 32px;
    color: #2e2e2e;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 5px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
}

.categorie-section .term-description {
    font-weight: 300;
}

.categorie-section {
    padding-bottom: 30px;
}

.posts-wrapper {
    display: flex;
    flex-direction: column;
}

.row {
    display: grid;
    grid-template-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px;
}

.row .post-item-large {
    grid-area: 1 / 1 / 1 / 3;
    height: 300px !important;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.row .post-item-small {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.post-item {
    background: white;
    transition: transform 0.3s;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.post-item:hover .post-thumbnail img {
    transform: scale(1.05);
}

.post-content {
    padding: 15px 0;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.post-title {
    font-size: 14px;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 5px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
}

.post-title a {
    color: #333;
    text-align: left;
    margin-bottom: 30px;
    letter-spacing: 5px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    text-decoration: none;
}

.post-title a:hover {
    color: #888;
}

.post-date {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    text-align: left;
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    gap: 10px;
    align-items: center;
}

.read-more {
    display: inline-block;
    justify-content: left !important;
    color: #666;
    text-decoration: none;
}


.posts-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
}

.posts-count {
    font-weight: 400;
    font-size: 14px;
}

.pagination {
    display: flex;
    align-items: center;
}

.pagination a {
    border: 1px solid #9c9c9c;
    color: #5F5753;
    padding: 5px 10px;
    text-decoration: none;
}

.pagination .fa {
    color: #5F5753;
    font-size: 14px;
}

.pagination .current {
    color: #5F5753;
    padding: 5px 10px;
    border: 1px solid #9c9c9c !important;
    font-weight: 400;
}

.woocommerce-pagination {
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .blog-section {
        padding: 10px;
        margin: 50px auto !important;
    }

    .page-title {
        text-align: center; /* Wyśrodkowanie tytułu */
        margin-bottom: 20px;
        font-size: 20px;
    }

    .mobile-post {
        display: flex;
        flex-direction: column;
    }

    .post-item {
        flex: 0 0 auto; /* Posty nie rosną, ani nie kurczą się */
        width: 100%; /* Każdy post zajmuje pełną szerokość kontenera */
    }

    .post-item {
        display: flex;
        flex-direction: column;
        width: 100%; /* Posty zajmują pełną szerokość kontenera */
        height: auto; /* Automatyczna wysokość */
    }

    .post-thumbnail img {
        width: 100%; /* Obrazek w pełnej szerokości */
        height: auto; /* Automatyczna wysokość */
        display: block; /* Usunięcie marginesów w poziomie */
    }

    .post-content {
        padding: 15px 0; /* Padding wewnętrzny dla treści */
    }

    .post-title {
        font-size: 16px; /* Zmniejszenie rozmiaru czcionki tytułu */
        text-align: left; /* Wyrównanie tytułu do lewej */
        margin-bottom: 10px; /* Odstęp poniżej tytułu */
    }

    .post-date {
        font-size: 12px; /* Rozmiar czcionki daty */
        color: #666; /* Kolor tekstu daty */
        margin-bottom: 10px; /* Odstęp poniżej daty */
    }

    .read-more {
        display: block; /* Blokowe wyświetlanie linku "Czytaj więcej" */
        text-align: left; /* Wyrównanie linku do lewej */
        margin-top: 10px; /* Odstęp nad linkiem */
        color: var(--text-gray); /* Kolor linku */
        text-decoration: none; /* Usunięcie podkreślenia linku */
    }

    .read-more:hover {
        color: #005177; /* Kolor linku po najechaniu */
    }
}


.faq-naglowek h1 {
    text-transform: uppercase;
    letter-spacing: 5px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
}

.faq-naglowek p {
    font-family: 'Lato', sans-serif;
    font-weight: 400;
}
.akordeon {
    border: 1px solid #ddd; /* Border around the accordion */
    border-radius: 5px; /* Rounded corners */
    overflow: hidden; /* To ensure the content doesn't overflow */
    margin: 25px auto; /* Margin around the accordion section */
    width: 1200px; /* Full width */
}

.akordeon-header {
    background-color: white; /* Light grey background */
    padding: 15px; /* Padding around the header */
    cursor: pointer; /* Change cursor to pointer */
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 5px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    display: flex; /* Use flexbox to align items */
    align-items: center; /* Center items vertically */
    justify-content: space-between; /* Space between the title and chevron */
    transition: background-color 0.3s; /* Smooth transition for background color */
}

.akordeon-content {
    padding: 15px; /* Padding around the content */
    display: none; /* Initially hide the content */
    background-color: #fff;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
}

.akordeon-content.active {
    display: block; /* Show content when active */
}

/* Stylizacja dla mobilnych urządzeń */
@media (max-width: 768px) {
    /* Nagłówek FAQ */
    .faq-naglowek h1 {
        margin-top: 50px; /* Zmniejszenie marginesu górnego */
        margin-left: 20px; /* Zmniejszenie marginesu bocznego */
        font-size: 24px; /* Zmniejszenie rozmiaru czcionki */
        letter-spacing: 2px; /* Zmniejszenie odstępu między literami */
    }

    .faq-naglowek p {
        margin-left: 20px; /* Zmniejszenie marginesu bocznego */
        font-size: 14px; /* Zmniejszenie rozmiaru czcionki */
    }

    /* Akordeon */
    .akordeon {
        width: 100%; /* Szerokość dopasowana do szerokości ekranu */
        margin: 15px auto; /* Zmniejszenie marginesu wokół akordeonu */
    }

    .akordeon-header {
        font-size: 16px; /* Zmniejszenie rozmiaru czcionki nagłówka */
        padding: 12px; /* Zmniejszenie paddingu */
        letter-spacing: 2px; /* Zmniejszenie odstępu między literami */
    }

    .akordeon-content {
        padding: 12px; /* Zmniejszenie paddingu wokół treści */
    }
}

.fa-chevron-down {
    transition: transform 0.3s; /* Smooth transition for rotation */
}

.fa-chevron-down.rotate {
    transform: rotate(180deg); /* Rotate chevron on click */
}
/* Stylizacja przycisku logowania */
.woocommerce-button {
    background-color: var(--text-gray);
    color: #fff;
    padding: 12px 20px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    margin-top: 10px;
}

.woocommerce-button:hover {
    background-color: #A9ABAC;
}

.mk h2 {
    margin-left: 50px;
    margin-bottom: 50px;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    letter-spacing: 5px;
    margin-top: 150px;
}

.woocommerce-cart-form {
    margin-left: 50px;
    margin-right: 50px;
    margin-top: 200px;
    border: 1px solid #ccc;
}

.woocommerce-cart-form a {
    text-decoration: none;
    color: var(--text-gray);
}

.woocommerce .cart-collaterals .cart_totals {
    margin-right: 50px !important;
}

.woocommerce .cart-collaterals .cart_totals h2 {
    font-family: 'Lato', sans-serif;
    font-weight: 400;
    letter-spacing: 5px;
    text-transform: uppercase;
}

.wc-proceed-to-checkout .checkout-button {
    background-color: var(--text-gray) !important;
    color: #fff;
    border-radius: 0 !important;
}

.wc-proceed-to-checkout .checkout-button:hover {
    background-color: #A9ABAC !important;
}

.woocommerce-cart table.cart td.actions .coupon .input-text {
    width: 100px !important;
    height: 35px;
}

.cart-empty {
    margin-top: 150px !important;
    margin-left: 50px !important;
    margin-right: 50px !important;
}

.return-to-shop {
    margin-left: 50px !important;
}

.checkout-container {
    display: flex !important;
    justify-content: space-between;
    flex-direction: row;
    gap: 15px;
}
.checkout-left {
    margin-left: 25px;
    width: 55%;
}

.dd-checkout-right-panel {
    max-width: 350px;
    padding: 25px;
    border: 2px solid rgb(229, 231, 235);
    position: sticky;
    top: var(--navbar-height);
}

@media screen and (max-width: 960px) {
    .dd-checkout-right-panel {
        width: auto;
        max-width: 100%;
    }
}

.checkout-right h2 {
    color: var(--text-gray);
    font-size: 18px;
    font-family: 'Lato', sans-serif !important;
    font-weight: 300 !important;
    letter-spacing: 5px !important;
    text-transform: uppercase !important;
    line-height: 28px;
}

#dd-checkout-items-summary ul.cart-items {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

#dd-checkout-items-summary .cart-item {
    margin-bottom: 15px;
}

#dd-checkout-items-summary .cart-item-header {
    display: flex;
    align-items: left;
}

#dd-checkout-items-summary .cart-item-image {
    min-width: 64px;
    max-width: 64px;
    max-height: 64px;
    margin-right: 10px;
}

#dd-checkout-items-summary .cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#dd-checkout-items-summary .cart-item-details {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.cart-item a {
    text-decoration: none;
    width: 100%;
}

#dd-checkout-items-summary .product-name {
    text-align: left !important;
    font-weight: 400 !important;
    font-size: 14px;
}

#dd-checkout-items-summary .product-sku {
    font-size: 12px;
    color: #999;
    font-weight: 300 !important;
}

#dd-checkout-items-summary .product-attributes {
    font-size: 14px;
    color: #999;
    font-weight: 300;
    width: 100%;
}

#dd-checkout-items-summary .cart-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    width: 100%;
}

#dd-checkout-items-summary .cart-quantity input {
    width: 64px;
    padding: 8px;
    border-radius: 0 !important;
    border: 1px solid rgb(243, 244, 246);
}

#dd-checkout-items-summary .cart-price {
    display: flex;
    justify-content: flex-end;
    font-weight: 400;
}

#dd-checkout-items-summary .checkout-remove-item {
    color: var(--text-gray) !important;
    cursor: pointer;
}

#dd-checkout-items-summary .cart-remove {
    color: rgb(75, 85, 99) !important;
}

#dd-checkout-items-summary .cart-remove:hover {
    color: var(--primary-green) !important;
    cursor: pointer;
}

#dd-checkout-items-summary .cart-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    font-weight: 400;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.checkout-cart-summary {
    margin: 20px 0;
}

.checkout-cart-shipping, .checkout-cart-total {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #ddd; /* Delikatna linia oddzielająca sekcje */
}

.checkout-cart-shipping-left, .checkout-cart-total-left {
    font-weight: bold;
}

.checkout-cart-shipping-right, .checkout-cart-total-right {
    font-weight: bold;
    color: #333; /* Możesz zmienić kolor według potrzeb */
}

.checkout-cart-total {
    border-top: 1px solid #ddd; /* Delikatna linia oddzielająca od sekcji dostawy */
    margin-top: 10px;
    padding-top: 10px;
}


.shipping-methods-wrapper {
    display: grid;
    grid-template-columns: auto auto;
    grid-gap: 16px;
    margin-bottom: 16px;
}

.shipping-method-button {
    background-color: white !important;
    border: 1px solid rgb(245, 245, 244) !important;
    width: 100%;
    height: 50px !important;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
    color: var(--text-gray);
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
    padding: 16px;
}

.shipping-method-button .price {
    display: flex;
    height: 100%;
    align-items: center;
    margin-bottom: 0 !important;
}

.shipping-method-button.selected {
    border: 1px solid rgb(74, 106, 101) !important;
    box-shadow: rgb(255, 255, 255) 0px 0px 0px 0px, rgb(74, 106, 101) 0px 0px 0px 1px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
}

.shipping-method-button:hover {
    background: #f8f8f8 !important;
}

.wc_payment_methods {
    display: flex;
    flex-wrap: wrap;
    gap: 5px; /* Odstęp między przyciskami */
    list-style-type: none; /* Usuń znaczniki listy */
    padding: 0;
}

.payment-method-button {
    background-color: white !important;
    border: 1px solid #ddd !important;
    width: auto !important;
    height: 50px !important;
    cursor: pointer;
    display: flex !important;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
    color: var(--text-gray);
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px !important;
    padding: 8px 20px;
    margin-right: 10px;
    gap: 8px;
}

.payment-method-button.selected {
    border: 2px solid rgb(22, 163, 74) !important;
    background-color: rgb(240, 253, 244) !important;
    color: rgb(21, 128, 61) !important;
}

.payment-method-button:hover {
    background-color: #fafafa !important;
    color: var(--text-gray);
}

/* Stylizacja akordeonu */
.checkout-accordion {
    display: block;
    width: 55%;
    margin: 0 auto; /* Wyśrodkowanie */
}

.checkout-accordion-item {
    margin-bottom: 20px;
}

.checkout-accordion-header img {
    width: 25px;
    height: 25px;
    color: #555;
    margin-right: 15px;
}

.checkout-accordion-header {
    background-color: rgb(245, 245, 244);
    padding: 25px;
    color: #5F5753;
    font-size: 18px;
    font-family: 'Lato', sans-serif !important;
    font-weight: 400 !important;
    letter-spacing: 5px !important;
    text-transform: uppercase !important;
    align-content: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.checkout-accordion-header div {
    display: flex;
    align-items: center;
}

.checkout-accordion-header input[type="checkbox"] {
    display: flex;
    float: right !important;
    width: 10px;
    height: 10px;
    cursor: pointer;
    margin-left: 100px;
}

.checkout-accordion-header label {
    font-family: 'Lato', sans-serif !important;
    font-size: 16px !important;
    letter-spacing: 0;
    text-transform: none !important;
    margin-left: 10px;
    display: inline;
    float: right !important;
}

/* Nagłówki z WooCommerce */
.woocommerce-order-details__title,
.woocommerce-column__title,
.woocommerce-Address-title h2,
.woocommerce-additional-fields h3
{
    font-family: 'Lato', sans-serif !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    text-transform: uppercase;
    letter-spacing: 5px;
}

.checkout-accordion-content {
    padding: 20px;
    background: white;
    border: 1px solid rgb(245, 245, 244);
}

#dd-checkout-shipping-section {
    display: flex;
    flex-direction: column;
}

.easypack-visible-point-header {
    background: transparent !important;
    font-style: normal !important;
    box-shadow: inset 0 -4px #ffcd00;
}

.checkout-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.checkout-column {
    width: 50%;
    padding-right: 10px;
    box-sizing: border-box;
}

.checkout-column input,
.checkout-row select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.checkout-row select {
    width: 100%;
}

.checkout-save-button {
    background-color: #4A6A65;
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 0;
    float: right;
    font-size: 16px;
}

.checkout-save-button:hover, #place-older:hover {
    background-color: #4A6C83;
    cursor: pointer;
}

.checkout-row:last-child {
    justify-content: flex-end; /* Wyrównanie przycisku do prawej */
}

.checkout-column:nth-child(3) {
    width: 33.33%;
    padding-right: 10px;
}

.checkout-column:last-child {
    padding-right: 0;
}

.checkout-column:nth-child(2n+1) {
    padding-right: 10px;
}


/* Po otwarciu akordeonu */
.checkout-accordion-section.active .accordion-content {
    display: block;
}

/* Stylizacja inputów */
.checkout-accordion-content label {
    font-weight: 300;
    font-size: 14px;
    display: block;
    margin-bottom: 10px;
}

.checkout-accordion-content input[type="text"],
.checkout-accordion-content input[type="number"],
.checkout-accordion-content input[type="email"],
.checkout-accordion-content input[type="tel"],
.checkout-accordion-content input[type="password"],
.checkout-accordion-content textarea,
.checkout-accordion-content select {
    width: 100%;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 0;
    font-size: 14px;
    font-weight: 400;
    box-sizing: border-box;
}

.checkout-accordion-content #payment_methods ul {
    list-style: none !important;
}

.required {
    color: red;
}

.checkout-accordion-header label {
    cursor: pointer;
}

.woocommerce-billing-fields h3 {
    display: none;
}

button[name="apply_coupon"] {
    background-color: #4A6A65 !important;
    color: #fff;
    border-radius: 0 !important;
    border: none;
    padding: 15px 30px;
    text-align: center;
    box-shadow: none !important;
    color: white;
    font-size: 16px;
}

button[name="apply_coupon"]:hover {
    background-color: #4A6C83 !important;
}

/* Dostosowanie do urządzeń mobilnych */
@media  (max-width: 960px) {
    .checkout-container {
        margin-top: 0;
        flex-direction: column; /* Układ pionowy na małych ekranach */
        justify-content: center;
    }

    .checkout-left, .checkout-right {
        width: 100%; /* Oba kontenery mają pełną szerokość */
        margin: 0;
    }

    .checkout-right {
        margin-top: 20px;
        width: auto;
    }

    .checkout-accordion {
        width: 100%;
        margin: 0 auto;
    }

    #dd-checkout-items-summary .cart-item-image {
        width: 100px; /* Zwiększ rozmiar obrazka na urządzeniach mobilnych */
        margin-bottom: 10px;
    }

    #dd-checkout-items-summary .checkout-product-name {
        margin-right: 0;
        text-align: left;
        font-size: 16px; /* Zmniejsz rozmiar czcionki na mniejszych ekranach */
    }

    #dd-checkout-items-summary .cart-item-footer {
        align-items: flex-start;
    }

    #dd-checkout-items-summary .cart-quantity input {
        margin-left: 0;
        width: 60px;
        margin-bottom: 10px;
    }

    .checkout-save-button {
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .checkout-column {
        width: 50%; /* Pełna szerokość kolumn w układzie pionowym */
        padding-right: 0;
    }

    .checkout-column input,
    .checkout-row select {
        width: 50%;
    }


    .checkout-accordion-header {
        padding: 30px;
        font-size: 16px;
    }

    .checkout-accordion-header input[type="checkbox"] {
        margin-left: 10px;
    }

    button[name="apply_coupon"] {
        width: 100%; /* Przycisk na pełną szerokość na mobilnych */
    }

    .checkout-cart-summary {
        flex-direction: column; /* Przełącz na układ pionowy dla podsumowania */
        text-align: left;
    }

    .checkout-cart-summary div {
        width: 100%;
    }
}


.related-products {
    margin-top: 30px;
}

.related-products h2 {
    font-size: 24px;
    margin-bottom: 15px;
}

.related-products-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.related-products .product {
    flex: 1 1 calc(25% - 15px); /* 4 produkty w rzędzie z przerwami */
    box-sizing: border-box;
}

.wc-block-grid__products {
    width: 100%;
    list-style: none;
    display: flex !important;
    margin-left: 0;
    margin-right: 25px;
    padding-left: 0;
}

.wc-block-grid__products a {
    font-size: 16px;
    color: var(--text-gray);
    text-decoration: none;
    text-align: center;
}
.wc-block-grid__products .price {
    font-size: 16px !important;
    font-weight: 300 !important;
    color: var(--text-gray);
    text-decoration: none;
    text-align: center;
}

.logowanie {
    background-color: #EEEEEF;
    margin-left: auto;
    margin-right: auto;
    padding: 20px 0 60px;
}

.rejestracja {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50px;
    background-color: #EEEEEF;
    padding: 20px;
}

.odyzskiwanie {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50px;
    background-color: #EEEEEF;
    padding: 20px;
}

.logowanie-header, .rejestracja-header {
    font-family: 'Lato', sans-serif;
    font-size: 40px;
    margin-bottom: 10px;
    font-weight: 400;
    color: var(--text-gray);
    text-align: center;
    letter-spacing: 8px;
    text-transform: uppercase;
}

.logowanie-description, .rejestracja-description {
    font-family: 'Lato', sans-serif;
    font-size: 14px;
    margin-bottom: 20px;
    text-align: center;
}

.logowanie-box {
    margin: 0 auto;
    max-width: 400px;
    border: 1px solid #ddd;
    padding: 20px;
    background-color: white;
    max-height: 800px;
}

.rejestracja-box {
    margin: 0 auto;
    max-width: 400px;
    border: 1px solid #ddd;
    padding: 20px 40px;
    background-color: white;
}

.odzyskiwanie-box {
    margin: 0 auto;
    max-width: 400px;
    border: 1px solid #ddd;
    padding: 40px;
    background-color: white;
}

/* Dostosowanie do urządzeń mobilnych */
@media only screen and (max-width: 768px) {
    .logowanie {
        margin-top: 0;
    }

    .rejestracja {
        margin-top: 0 !important;
    }

    .odzyskiwanie {
        margin-top: 0;
    }

    .logowanie-box, .rejestracja-box .odzyskiwanie-box {
        margin: 0 auto;
        max-width: 100%;
        border: 1px solid #ddd;
        padding: 20px;
        background-color: white;
        height: 450px;
    }
}

.woocommerce-form-login {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}

/* Style dla każdej sekcji formularza */
.form-row {
    margin-bottom: 20px;
}

/* Link "Nie pamiętasz hasła?" pod inputem "Hasło" */
.form-row-lost-password {
    text-align: right;
}

/* "Zapamiętaj mnie" po lewej stronie */
.form-row-rememberme {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

/* Link rejestracji wyśrodkowany pod przyciskiem logowania */
.form-row-register {
    align-items: center;
    width: 100%;
    text-align: center;
    padding-top: 25px !important;
    clear: both;
}

.register-link a {
    margin-top: 25px !important;
    text-decoration: none;
    font-weight: 400;
}

.woocommerce-Input {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ddd;
}

label {
    font-family: 'Lato', sans-serif;
    color: var(--text-gray) !important;
    font-size: 14px !important;
    font-weight: 300 !important;
}

.woocommerce-form-login__rememberme {
    margin-top: 25px !important;
}

.lost-password-link {
    display: block;
    float: right !important;
    color: #0073aa;
    text-decoration: none;
}

.register-link a {
    text-decoration: none;
    color: #0073aa;
}

.register-link a:hover {
    text-decoration: underline;
}

.woocommerce-form-register .form-row {
    margin-bottom: 15px;
}

.woocommerce-form-register input {
    max-width: 400px;
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    box-sizing: border-box;
}

.woocommerce-form__input-checkbox {
    padding: 0 !important;
    width: 15px !important;
    height: 15px;
    margin-right: 10px;
}

.woocommerce-form-row-checkbox {
    width: 100%;
    display: flex;
    justify-content: left;
    align-content: left;
    margin-top: 10px;
    padding: 0 !important;
}

.woocommerce-form-register__terms {
    display: flex;
    gap: 0;
    padding: 0 !important;
}

/* Stylizacja siatki produktów */
.products-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Odstęp między produktami */
    justify-content: flex-start; /* Produkty wyrównane do lewej */
}

/* Stylizacja pojedynczej karty produktu */
.product-card {
    flex: 1 1 calc(25% - 20px); /* 4 produkty w rzędzie z odstępem */
    box-sizing: border-box;
    margin-bottom: 30px; /* Odstęp między rzędami */
    list-style: none; /* Usunięcie stylów listy */
}

/* Stylizacja linków wewnątrz karty produktu */
.product-link {
    text-decoration: none;
    color: inherit;
    display: block;
    border-radius: 0;
    background: #fff;
    text-align: center;
}

/* Stylizacja obrazu produktu */
.product-image  {
    display: flex;
    justify-content: center;
    align-items: center;
}
.product-image img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Stylizacja nazwy i ceny produktu */
.product-name {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
}

.product-price {
    font-size: 14px;
    color: #333;
    text-align: center !important;
    font-weight: 700;
}

/* Stylizacja etykiet produktów */
.product-label {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 3px;
    z-index: 10;
}

.product-label.sale {
    background-color: #e74c3c; /* Czerwony kolor dla promocji */
}

.product-label.new {
    background-color: #2ecc71; /* Zielony kolor dla nowości */
}

/* Ustawienie kontenera zdjęcia produktu */
.woocommerce ul.products li.product {
    position: relative;
}

/* Przykładowe stylizacje */
.woocommerce ul.products li.product img {
    width: 100%;
    height: auto;
}

.woocommerce-checkout-review-order-table {
    border: none !important;
}

.woocommerce-checkout-review-order-table th {
    font-size: 14px;
    font-family: 'Lato', sans-serif;
    color: rgb(68, 64, 60);
    font-weight: 300 !important;
}

.woocommerce-checkout-review-order-table td {
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    color: rgb(68, 64, 60);
    font-weight: 300 !important;
    text-align: right;
}

.woocommerce-checkout-review-order-table .order-total td {
    font-size: 18px;
    font-family: 'Lato', sans-serif;
    color: rgb(68, 64, 60);
    font-weight: 600 !important;
    text-align: right;
}

.woocommerce table.shop_table tbody th, .woocommerce table.shop_table tfoot td, .woocommerce table.shop_table tfoot th {
    border-top :none;
}

.woocommerce-checkout-payment {
    background: none !important;
    font-weight: 300 !important;
}

#place_order {
    font-size: 16px;
    padding: 15px 30px;
    border-radius: 0;
    border: 0;
    margin-top: 1rem;
    cursor: pointer;
    font-weight: 400;
    color: white;
    background-color: #4A6A65;
}

.dd-checkout-button {
    font-size: 16px !important;
    padding: 15px 30px !important;
    border-radius: 0 !important;
    font-weight: 400 !important;
    background: rgb(74, 106, 101) !important;
    color: white !important;
}

.dd-checkout-button:hover {
    opacity: 0.7;
    background: rgb(74, 106, 101) !important;
}

.woocommerce-remove-coupon {
    color: #a20a0a !important;
}

.woocommerce-customer-details address {
    padding: 15px !important;
}

.dd_product_tags {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.dd_product_tags a{
    color: #ababab;
    text-decoration: none;
    font-weight: 300;
}

.dd_product_tags a:hover{
    color: #757575;
}

.fa-trash:before {
    color: rgb(75, 85, 99) !important;
}

@media (max-width: 768px) {
    .woocommerce-checkout {
        margin-top: 0px;
    }

    .shipping-methods-wrapper {
        grid-template-columns: auto;
    }

    .dd-thankyou {
        margin-top: 0;
    }
}

/** Google maps at Lokalizator SPA */
.wpgmza-basic-list-item.wpgmza_div_title {
    font-size: 16px;
    font-family: Lato, sans-serif;
    letter-spacing: 4px;
    text-transform: uppercase;
    font-weight: 400;
    margin-bottom: 10px !important;
}

.wpgmza-basic-list-item.wpgmza_div_address {
    font-size: 14px !important;
    font-family: Lato, sans-serif;
    font-weight: 300;
    padding-left: 0 !important;
    color: var(--text-gray) !important;
}

.wpgmaps_blist_row {
    padding: 15px 20px !important;
}

.slick-track {
    display: flex !important;
    align-items: center;
}

.slick-dots {
    left: 0;
}

.dd-quantity-available {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3px;
}

.dd-quantity-available::before {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #699335;
    border-radius: 50%;
    margin-right: 4px;
    content: "";
}

.dd-quantity-not-available::before {
    background-color: #933535;
}

.woocommerce-info {
    border-top-color: var(--primary-green);
}

.woocommerce-info::before {
    color: var(--primary-green);
}

/** MUST BE ON END OF FILE */

.dd-mini-cart-free-shipping {
    display: flex;
    justify-content: start;
    margin-top: 20px;
    gap: 15px;
}

.dd-mini-cart-free-shipping img {
    width: 40px;
}

.dd-mini-cart-free-shipping p {
    margin: 0;
    font-weight: 300;
    flex-flow: 1;
}

.easypack_show_geowidget {
    font-size: 14px !important;
    font-family: 'Lato', sans-serif !important;
    float: left !important;
    border: 1px solid #FCC905 !important;
    background-color: #FCC905 !important;
    color: black !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    max-width: 100% !important;
    padding: 15px 45px !important;
    align-content: center !important;
    justify-content: left !important;
}
.easypack_show_geowidget i {
    margin-left: 50px !important;
}

.selected-parcel-machine {
    max-width: 100%;
    border: none !important;
    float: left;
}

.dd-product-full-description iframe, .page-content iframe, .entry-content iframe {
    width: 700px;
    height: 400px;
    max-width: 100%;
    align-self: start;
}

@media screen and (max-width: 760px) {
    .dd-product-full-description iframe, .page-content iframe {
        height: 250px;
    }
}

/*.dd-product-full-description p:has(> iframe) {*/
/*    text-align: center;*/
/*}*/

/* 2025 */

.aligncenter.is-type-video {
    text-align: center;
}

.alignright.is-type-video {
    text-align: right;
}

.alignleft.is-type-video {
    text-align: left;
}

/* WOOCOMMERCE */

.woocommerce-account .addresses .title .edit {
    float: left !important;
}

.woocommerce-account .woocommerce-address-fields button,
.woocommerce-MyAccount-content button {
    font-size: 16px;
    padding: 15px 30px;
    border-radius: 0;
    border: 0;
    margin-top: 1rem;
    cursor: pointer;
    font-weight: 400;
    color: white;
    background-color: #4a6a65;
}

.woocommerce-account .woocommerce-address-fields button:hover,
.woocommerce-MyAccount-content button:hover {
    background-color: #4a6c83;
    cursor: pointer;
}

.woocommerce-account .woocommerce form .form-row input.input-text,
.woocommerce form .form-row textarea {
    height: 35px;
}

@media screen and (max-width: 1200px) {
    .footer-newsletter {
        width: 100%;
    }

    .front-boxy > *:nth-child(3) {
        grid-column: 1 / -1;
    }
}