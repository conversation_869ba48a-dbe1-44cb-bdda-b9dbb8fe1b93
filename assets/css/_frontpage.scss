.main-swipper {
    overflow: hidden;
    background-color: $color-primary;

    .left-right {
        display: flex;
        position: relative;
        gap: 35px;

        .left-side {
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            flex-basis: 65%;
            min-width: 65%;
            padding: 3rem 0;
            color: $color-white;
            order: 1;

            .main-content {
                display: flex;
                flex-direction: column;
                gap: 25px;
                min-height: 455px;
                max-width: 70%;

                .headline {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    hr {
                        width: 40px;
                        margin: 0;
                    }

                    .subtitle {
                        font-style: normal;
                        font-weight: 300;
                        font-size: $font-size-16px;
                        line-height: 32px;
                        text-transform: uppercase;
                    }
                }

                .main-subtitle {
                    margin: 0;

                    hr {
                        width: 40px;
                        margin: 0;
                    }

                    .subtitle {
                        font-style: normal;
                        font-weight: 400;
                        font-size: $font-size-16px;
                        line-height: 32px;
                        text-transform: uppercase;
                    }
                }

                .main-title {
                    font-family: $font-secondary;
                    font-style: normal;
                    margin: 0;
                    font-weight: 400;
                    font-size: $font-size-60px;
                    line-height: 72px;
                    letter-spacing: -0.01em;
                }

                .main-subtitle {
                    font-family: $font-primary;
                    font-style: normal;
                    font-weight: 300;
                    font-size: $font-size-18px;
                    line-height: 155%;
                }

                .main-button {
                    padding: 15px 35px;
                    width: fit-content;
                    transition: $transition;

                    &:hover {
                        border: 1px solid $color-white;
                        color: $color-white;
                        transition: $transition;
                        background-color: $button-hover;
                    }
                }
            }
        }

        .right-side {
            position: fixed;
            top: 0;
            right: 0;
            width: 35vw;
            height: 100%;
            overflow: hidden;
            z-index: -1;
            display: flex;
            margin: 0;

            .right-side__img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center left;
            }

            ul {
                display: flex;
                flex-direction: column;
                gap: 5px;
                position: absolute;
                top: 9%;
                right: 10%;
                padding: 0;
                margin: 0;

                li {
                    list-style-type: none;
                    text-align: right;
                    text-transform: uppercase;
                    color: $color-black;
                    font-size: $font-size-16px;
                }
            }
        }
    }

    .button-wrapper {
        position: absolute;
        gap: 10px;
        right: 38%;
        bottom: 50px;
        z-index: 2;
        display: flex;
        align-items: center;
        cursor: pointer;

        .swiper-pagination,
        .swiper-button {
            width: 45px;
            height: 45px;
        }

        .swiper-progress {
            position: absolute;
            inset: 0;
            border: 3px solid $color-primary;
            border-radius: 50%;
            clip-path: polygon(0 0, 0 0, 0 0, 0 0);
            transform: rotate(-90deg);
            transition: clip-path 1s linear;
        }

        .swiper-pagination {
            font-family: $font-secondary;
            font-size: $font-size-20px;
            order: 2;
            font-weight: 400;
            width: 45px;
            height: 45px;
            border: 1px solid $color-white;
            overflow: hidden;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            position: relative;
            bottom: 0;
            color: $color-white;

            .swiper-pagination-current {
                margin-right: 2px;
                margin-bottom: 5px;
                z-index: 3;
            }

            .swiper-pagination-total {
                margin-left: 2px;
                margin-top: 5px;
            }
        }

        .swiper-button {
            border: 1px solid $color-white;
            border-radius: 50%;
            background-size: 35%;
            background-position: center;
            background-repeat: no-repeat;
            transition: all 0.3s ease-out;
            cursor: pointer;

            svg {
                display: none;
            }

            &:hover {
                background-color: $button-hover;
            }
        }

        .swiper-prev-button {
            order: 1;
            background-image: url(../../public/arrow-left.svg);
        }

        .swiper-next-button {
            order: 3;
            background-image: url(../../public/arrow-right.svg);
        }
    }

    .swiper-slide {
        opacity: 0 !important;
        transition: 0.4s;

        &-active {
            opacity: 1 !important;
        }
    }
}

.section-category {
    padding-top: 3rem;

    .wrapper {
        display: flex;
        align-items: center;
        font-style: normal;
        font-size: $font-size-18px;
        line-height: 155%;
        gap: 50px;
        padding-bottom: 3rem;

        .section-subtitle {
            text-transform: uppercase;
            font-family: $font-primary;
            font-weight: 400;
        }

        hr {
            width: 100px;
            margin: 0;
            height: 0;
        }

        .subtitle-text {
            color: #4d4c46;
        }
    }

    .grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        grid-column-gap: 40px;
        grid-row-gap: 50px;

        .category-item {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 170px;

            &:not(:last-child)::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.2);
                z-index: 1;
            }

            &:last-of-type {
                border: 1px solid $color-black;
                min-height: 170px;

                .category-title {
                    text-transform: capitalize;
                }

                .category-link {
                    border: none;
                    color: $color-black;

                    &:hover {
                        background-color: unset;
                    }
                }
            }

            .category-link {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                color: $color-white;
                border: 1px solid $color-white;
                transition: $transition;
                text-decoration: none;

                &:hover {
                    svg {
                        fill: $button-hover;
                        transition: $transition;
                    }
                }

                .category-image {
                    position: relative;
                    max-width: 100%;
                    width: 100%;
                    height: auto;
                    max-height: 170px;
                    object-fit: cover;
                    aspect-ratio: 1/1;
                }

                .category-content {
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    z-index: 2;

                    .category-title {
                        color: $color-white;
                    }

                    hr {
                        width: 40px;
                        margin: 0;
                    }

                    .insider {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 8px 12px;
                    }
                }

                .category-title {
                    font-family: $font-primary;
                    font-style: normal;
                    font-weight: 400;
                    font-size: $font-size-16px;
                    line-height: 150%;
                    text-transform: uppercase;
                }
            }
        }
    }
}

.section-bestseller {
    padding: 5rem 0;
    overflow: hidden;

    .section-title {
        font-family: $font-primary;
        font-style: normal;
        font-weight: 400;
        font-size: $font-size-40px;
        line-height: 130%;
        text-align: center;
        color: $color-black;
    }

    .swiper-button-container {
        display: none;
    }
}

.section-gray {
    background-color: #a9abaccc;
    padding: 50px 0;

    .inner {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .inner-text,
        .icon {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex-basis: calc(20% - 5px);
        }

        .icon {
            max-width: 100px;
        }

        .inner-text {
            gap: 5px;

            ul {
                display: flex;
                flex-direction: column;
                gap: 5px;
                margin-top: 0;
                margin-bottom: 10px;
                padding: 0;

                li {
                    text-align: center;
                    list-style-type: none;
                    font-weight: 300;
                }
            }

            .inner-title {
                font-family: $font-primary;
                font-style: normal;
                font-weight: 400;
                font-size: $font-size-32px;
                color: $color-black;
            }

            &:nth-child(1) {
                flex-basis: fit-content;
            }
        }

        @media (max-width: 768px) {
            flex-wrap: wrap;
            gap: 20px;

            .inner-text,
            .icon {
                flex-basis: 33%;
                max-width: 100px;
            }
        }
    }
}

.section-for {
    padding: 5rem 0;

    .category-section {
        display: flex;
        justify-content: space-between;
        padding-bottom: 5rem;

        .category-image {
            position: relative;
            display: flex;
            flex-basis: calc(30% - 25px);

            img {
                max-width: 100%;
                width: 100%;
                height: auto;
                object-fit: cover;
            }

            ul {
                display: flex;
                flex-direction: column;
                gap: 5px;
                position: absolute;
                top: 25px;
                left: 25px;
                padding: 0;
                margin: 0;

                li {
                    list-style-type: none;
                    text-transform: uppercase;
                    color: $color-white;
                    font-size: $font-size-16px;
                }
            }
        }

        .category-content {
            flex-basis: calc(70% - 25px);
            min-width: calc(70% - 25px);

            .category-text {
                display: flex;
                justify-content: space-between;
                font-style: normal;
                font-size: $font-size-48px;
                line-height: 110%;
                letter-spacing: -0.01em;

                .main-button {
                    padding: 0 25px;
                    width: fit-content;
                    height: fit-content;

                    &.reverse {
                        color: $color-black;
                        border: 1px solid $color-black;
                        transition: $transition;
                    }

                    &:hover {
                        background-color: $swiper-button-hover;
                        color: $color-black;
                        transition: $transition;
                    }
                }

                .category-title {
                    font-size: $font-size-48px;
                    font-family: $font-secondary;
                    font-weight: 400;
                    margin: 0 0 20px 0;
                }
            }

            .swiperFor {
                .swiper-wrapper {
                    .swiper-slide {
                        flex-shrink: 1;

                        .product {
                            .product-item {
                                padding: 0;

                                .product-variations {
                                    min-height: 90px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.section-quality {
    background-color: #f8f8f8;
    padding: 3rem 0;

    .wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 30px;

        .insider {
            display: flex;
            justify-content: space-between;
            width: 100%;
            gap: 30px;

            .column {
                display: flex;
                align-items: center;

                .content {
                    display: flex;
                    align-items: center;
                    justify-content: end;
                    gap: 15px;
                    width: 100%;

                    .icon {
                        max-width: 160px;
                    }

                    .text {
                        font-family: $font-secondary;
                        font-style: normal;
                        text-align: right;
                        font-weight: 400;
                        font-size: $font-size-24px;
                        line-height: 130%;
                        color: $color-black;
                        white-space: pre;
                    }
                }

                .section-title {
                    font-family: $font-secondary;
                    font-style: normal;
                    font-weight: 400;
                    font-size: $font-size-48px;
                    line-height: 72px;
                    letter-spacing: -0.01em;
                    color: $color-black;
                }
            }
        }

        .bottom-text {
            width: 100%;
            font-family: $font-primary;
            font-style: normal;
            font-weight: 400;
            font-size: $font-size-14px;
            line-height: 110%;
            letter-spacing: 0.16em;
            text-transform: uppercase;
            color: #4d4c46;
        }
    }
}

.opinie-section {
    padding: 70px 0;

    .section-title {
        text-align: center;
        font-family: $font-primary;
        font-style: normal;
        font-weight: 400;
        font-size: $font-size-40px;
        line-height: 130%;
        color: $color-black;
        margin-bottom: 40px;
    }
}

.section-inspired {
    padding-bottom: 2rem;

    .inspired-wrapper {
        display: flex;
        gap: 50px;
        overflow: hidden;

        .inspired-image {
            flex-basis: calc(65% - 25px);
            position: relative;

            .section-title {
                margin-top: 0;
                font-family: $font-primary;
                font-style: normal;
                font-weight: 400;
                font-size: $font-size-40px;
                line-height: 130%;
                color: $color-black;
            }

            .video-container {
                min-height: 500px;
            }
        }

        .swiperInspired {
            display: flex;
            flex-direction: column;
            max-width: calc(35% - 25px);

            .section-subtitle {
                font-family: $font-primary;
                font-style: normal;
                text-align: center;
                font-weight: 400;
                font-size: $font-size-28px;
                line-height: 130%;
                letter-spacing: -0.02em;
            }

            .swiper-button-container {
                position: relative;
                top: 30px;
                width: 100%;
                display: flex;
                justify-content: center;
                margin: 15px 0;

                .swiper-button-next {
                    right: 35%;
                    left: auto;
                }

                .swiper-button-prev {
                    right: auto;
                    left: 35%;
                }
            }

            .swiper-slide {
                .product {
                    .product-item {
                        .product-link {
                            .dd-product-image {
                                img {
                                    height: fit-content !important;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.section-inspired-posts {
    .dd-container {
        position: relative;

        .section-title {
            font-family: $font-primary;
            font-style: normal;
            font-weight: 400;
            font-size: $font-size-28px;
            line-height: 130%;
            letter-spacing: -0.02em;
        }

        .swiper-button-container {
            position: absolute;
            top: 20px;
            right: 105px;

            .swiper-button-next {
                left: var(--swiper-navigation-sides-offset, 5px);
                right: auto;
            }

            .swiper-button-prev {
                right: var(--swiper-navigation-sides-offset, 20px);
                left: auto;
            }
        }

        .swiperInspiredPosts {
            .swiper-wrapper {
                .swiper-slide {
                    .post-thumbnail {
                        img {
                            max-height: 155px;
                        }
                    }
                }
            }
        }
    }
}

.section-cards {
    padding: 6rem 0;
    overflow: hidden;

    .wrapper {
        display: flex;
        flex-direction: column;

        .card {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-height: 170px;
            height: auto;
            color: $color-white;
            transition: max-height 1s ease-in-out;
            overflow: hidden;
            position: relative;

            &:nth-child(1) {
                background-color: #b24837;
                min-height: 180px;
            }

            &:nth-child(2) {
                background-color: #4a6a65;
            }

            &:nth-child(3) {
                background-color: #a9abac;
            }

            &.open {
                max-height: 5000px;
                transition: max-height 1s ease-in-out;
            }

            .header-title {
                font-family: $font-primary;
                font-style: normal;
                font-weight: 400;
                font-size: $font-size-16px;
                line-height: 32px;
                text-transform: uppercase;
            }

            .card-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 3rem 0;
                gap: 10px;

                .icon {
                    display: flex;
                    align-items: center;
                    gap: 25px;
                    min-width: 510px;
                    cursor: pointer;

                    svg {
                        transition: $transition;

                        &:hover {
                            transition: $transition;
                            fill: $button-hover;
                        }
                    }

                    .section-title {
                        font-family: $font-primary;
                        font-style: normal;
                        font-weight: 300;
                        font-size: 36px;
                        line-height: 110%;
                        margin: 0;
                        letter-spacing: -0.01em;
                    }
                }

                .section-subtitle {
                    font-family: $font-primary;
                    font-style: normal;
                    font-weight: 300;
                    font-size: 18px;
                    line-height: 24px;
                    max-width: 350px;
                }

                .main-button {
                    padding: 15px 35px;
                    width: fit-content;
                    transition: $transition;

                    &:hover {
                        border: 1px solid $color-white;
                        color: $color-white;
                        transition: $transition;
                        background-color: $button-hover;
                    }
                }
            }

            hr {
                margin: 10px 0;
                border: 0;
                border-top: 1px solid #fff;
            }

            .black_text {
                hr {
                    border-color: #000000 !important;
                }
            }

            .card-content {
                display: flex;
                justify-content: space-between;
                gap: 50px;
                padding: 3rem 0;

                .image {
                    flex-basis: calc(25% - 25px);

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .description {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    flex-basis: calc(30% - 25px);
                    font-family: $font-primary;
                    font-style: normal;
                    font-weight: 300;
                    font-size: $font-size-18px;
                    line-height: 24px;
                }

                .products {
                    display: flex;
                    flex: 1 1 0;
                    justify-content: space-between;
                    gap: 30px;

                    .product-item {
                        min-width: 50%;

                        &::after {
                            display: none;
                        }

                        .product {
                            background-color: $color-white;
                            padding: 0 1rem 1rem 1rem;
                            height: 100%;

                            .product-item {
                                height: fit-content;
                                justify-content: flex-start;

                                .product-link {
                                    .price {
                                        margin-top: 0;
                                    }
                                }

                                .product-variations {
                                    min-height: fit-content;

                                    .variations {
                                        .variation-button {
                                            color: $color-black;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.section-movie {
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 25px;

        .section-header {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .section-title {
                font-family: $font-primary;
                text-transform: uppercase;
                font-style: normal;
                margin: 0;
                font-weight: 400;
                font-size: $font-size-60px;
                line-height: 72px;
                color: $color-black;
            }

            .section-subtitle {
                font-family: $font-primary;
                font-style: normal;
                font-weight: 400;
                font-size: $font-size-18px;
                line-height: 155%;
                color: #4d4c46;
                max-width: 35%;
                text-align: center;
            }
        }

        .section-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;

            .movie {
                display: flex;
                flex: 1 1 75%;

                .video-container {
                    min-height: 590px;
                }
            }

            .movie-info {
                flex: 1 1 25%;
                display: flex;
                background-color: #a9abaccc;
                border: 1px solid #a9abaccc;
                min-height: 590px;

                .insider {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                    padding: 3rem;
                    gap: 15px;
                }

                hr {
                    width: 100%;
                    border: 0;
                    border-top: 1px solid $color-black;
                    margin: 0.5rem 0;
                }
            }
        }
    }
}

.home .partnerzy {
    padding: 3rem 0;
    border: 0;
}

.section-find-us {
    display: flex;
    background-color: #f8f8f8;
    overflow: hidden;

    .wrapper {
        display: flex;
        justify-content: space-between;

        .insider {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex-basis: 50%;
            padding: 2rem 4rem 0 0rem;
            gap: 25px;

            .section-title {
                font-family: $font-secondary;
                font-style: normal;
                margin: 0;
                font-weight: 400;
                font-size: $font-size-60px;
                line-height: 72px;
                letter-spacing: -0.01em;
            }

            .section-subtitle {
                font-family: $font-primary;
                font-style: normal;
                font-weight: 400;
                font-size: $font-size-16px;
                line-height: 150%;
                color: #4d4c46;
            }
        }

        .buttons {
            display: flex;
            gap: 20px;

            .main-button {
                padding: 15px 25px;
                width: calc(55% - 10px);
                height: fit-content;
                text-align: center;

                &.primary {
                    background: $color-primary;
                    color: $color-white;
                    transition: $transition;
                    border: 1px solid $color-primary;

                    &:hover {
                        background: $color-primary-hover;
                        border: 1px solid $color-primary-hover;
                    }
                }

                &.reverse {
                    color: $color-black;
                    border: 1px solid $color-black;
                    transition: $transition;

                    &:hover {
                        background-color: $swiper-button-hover;
                        color: $color-black;
                        transition: $transition;
                    }
                }
            }
        }
    }

    .image {
        margin-right: -100vw;
        max-width: 100vw;
        position: relative;
        right: 48%;
        width: 100vw;
        display: flex;

        img {
            height: 550px;
            max-width: 100%;
            width: 50%;
            object-fit: cover;
        }
    }
}

.section-more-inspiration {
    padding: 3rem 0;

    .wrapper {
        .section-title {
            font-family: $font-primary;
            font-style: normal;
            font-weight: 400;
            font-size: $font-size-40px;
            line-height: 130%;
            text-align: center;
            margin-bottom: 3rem;
        }
    }

    .swiper-slide {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .post-thumbnail {
            position: relative;

            img {
                max-height: 230px;
                object-fit: cover;
                object-position: center;
            }

            .video-duration {
                position: absolute;
                bottom: 0px;
                right: 5px;
                background: rgba(0, 0, 0, 0.6);
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 3px 6px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                line-height: 1;
            }
        }

        .post-link {
            text-decoration: none;

            svg {
                filter: $filter;
                transition: $transition;

                &:hover {
                    fill: $swiper-button-hover;
                    transition: $transition;
                    filter: unset;
                }
            }

            .post-info {
                display: flex;
                flex-direction: row;
                gap: 35px;

                .left {
                    display: flex;
                    flex-direction: column;
                    flex: 1 1 0;

                    .post-title {
                        font-family: $font-primary;
                        font-style: normal;
                        font-weight: 400;
                        font-size: $font-size-18px;
                        line-height: 155%;
                        text-transform: uppercase;
                        letter-spacing: normal;
                        color: $color-black;
                    }

                    .post-date {
                        color: $color-black;
                    }
                }

                .button {
                    margin: 10px 0;
                }
            }
        }
    }
}

.section-inspired,
.section-movie {
    .video-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding-top: 56.25%;

        .responsive-iframe {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            width: 100%;
            height: 100%;
        }

        .video-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-size: cover;
            z-index: 2;
            cursor: pointer;

            img {
                width: 100%;
                min-height: 535px;
                height: 100%;
                object-fit: cover;
                object-position: top left;
            }
        }
    }
}

.section-bestseller,
.section-inspired,
.section-for {
    .swiper-wrapper {
        position: relative;

        .swiper-slide {
            height: auto;

            .product {
                display: flex;
                flex-direction: column;
                height: 100%;

                .product-item {
                    padding-top: 4rem;
                    height: 100%;
                    width: 100%;

                    .product-link {
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                    }

                    .price {
                        margin-top: auto;
                        margin-bottom: 1rem;
                    }
                }
            }
        }
    }
}

.wpisy-section {
    padding-bottom: 3rem;
}

@media (max-width: $media-hd) {
}

@media (max-width: $media-laptop) {
    .section-quality {
        .wrapper {
            .insider {
                flex-wrap: wrap;
            }
        }
    }
    .section-gray {
        .left-right {
            flex-direction: column;
        }
    }

    .opinie-section {
        .ti-widget.ti-goog {
            .ti-reviews-container {
                .ti-controls {
                    display: flex !important;

                    .ti-next {
                        right: 42% !important;
                    }

                    .ti-prev {
                        left: 42% !important;
                    }
                }
            }
        }
    }

    .section-inspired {
        .inspired-wrapper {
            flex-direction: column;
            gap: 35px;

            .inspired-image {
                flex-basis: 100%;

                img {
                    display: flex;
                }

                .section-title {
                    font-size: $font-size-32px;
                }
            }

            .swiperInspired {
                max-width: 100%;

                .section-subtitle {
                    font-size: $font-size-22px;
                }
            }
        }
    }

    .section-cards {
        .wrapper {
            .card {
                .card-header {
                    .icon {
                        min-width: unset;
                    }

                    .main-button {
                        text-align: center;
                        padding: 15px;
                    }
                }
            }
        }
    }

    .section-find-us {
        .wrapper {
            flex-direction: column;
            gap: 35px;

            .insider {
                .buttons {
                    flex-wrap: wrap;

                    .main-button {
                        width: 100%;
                    }
                }
            }

            .image {
                max-width: 100vw;
                position: relative;
                right: 8%;
                min-width: 115vw;

                img {
                    height: 375px;
                    max-width: 100%;
                    width: 100%;
                    object-fit: cover;
                }
            }
        }
    }
}

@media (max-width: $media-tablet) {
    .main-swipper {
        .left-right {
            flex-direction: column-reverse;

            &.dd-container {
                padding: 0;
            }

            .left-side {
                flex-basis: 100%;
                min-width: 100%;
                padding: 2rem 50px;

                .main-content {
                    max-width: 100%;
                    min-height: fit-content;
                    justify-content: center;

                    .main-title {
                        font-size: $font-size-36px;
                        line-height: 1.2;
                    }

                    .main-subtitle {
                        font-size: $font-size-16px;
                    }

                    .main-button {
                        padding: 15px;
                    }
                }
            }

            .right-side {
                display: flex !important;
                position: relative;
                width: 100%;

                .right-side__img {
                    height: 350px;
                }

                ul {
                    top: 8%;
                    right: 8%;
                }
            }
        }

        .button-wrapper {
            right: 50px;
            bottom: 55%;
            height: fit-content;
        }
    }

    .section-category {
        padding-top: 5rem;

        .wrapper {
            align-items: center;
            gap: 25px;
            padding-bottom: 2rem;

            hr {
                border: 1px solid black;
                width: 100%;
            }

            .section-subtitle {
                margin: 5px 0 0 0;
            }
        }

        .subtitle-text {
            padding-bottom: 2rem;
        }
    }

    .section-gray {
        .left-right {
            .inner-left {
                .dd-black-border-button {
                    padding: 15px;
                }
            }

            .inner-right {
                flex-wrap: wrap;
                justify-content: space-around;
                gap: 30px;

                .inner-text,
                .icon {
                    flex-basis: calc(50% - 15px);

                    &:nth-child(1) {
                        flex-basis: calc(50% - 15px);
                    }
                }

                .icon {
                    width: 150px;
                }
            }
        }
    }

    .section-inspired {
        .inspired-wrapper {
            .inspired-image {
                img {
                    min-height: 100%;
                    object-position: center;
                }

                .section-title {
                    font-size: $font-size-24px;
                    text-align: center;
                }

                .wrapper {
                    top: unset;
                    bottom: 15px;
                    width: 250px;

                    .top {
                        font-size: $font-size-24px;
                    }

                    .bottom {
                        width: 100%;
                        font-size: $font-size-14px;
                    }
                }
            }
        }
    }

    .section-inspired,
    .section-movie {
        .video-container {
            min-height: 175px !important;

            .video-placeholder {
                img {
                    min-height: fit-content;
                    object-position: center;
                }
            }
        }
    }

    .section-bestseller {
        padding-bottom: 3rem;

        .wrapper {
            position: relative;

            .swiper-button-container {
                position: absolute;
                display: flex;
                justify-content: center;
                width: 100%;

                .swiper-button-next {
                    right: 35%;
                    left: auto;
                }

                .swiper-button-prev {
                    right: auto;
                    left: 35%;
                }
            }
        }

        .product {
            .product-item {
                padding-top: 1rem;

                img {
                    height: 300px !important;
                }
            }
        }
    }

    .section-for {
        .category-section {
            flex-direction: column;
            gap: 35px;
            overflow: hidden;

            .category-image {
                ul {
                    top: 30%;
                }

                img {
                    max-height: 135px;
                    object-position: 250% 45%;
                }
            }

            .category-content {
                flex-basis: 100%;
                min-width: 100%;
                position: relative;

                .swiper-button-container {
                    position: absolute;
                    display: flex;
                    justify-content: center;
                    width: 100%;
                    top: 25px;

                    .swiper-button-next {
                        right: 0;
                        left: auto;
                    }

                    .swiper-button-prev {
                        display: none;
                    }
                }

                .swiperFor {
                    .swiper-wrapper {
                        .swiper-slide {
                            flex-shrink: 0;

                            .product {
                                .product-item {
                                    padding-top: 1rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .section-quality {
        .wrapper {
            gap: 30px;

            .insider {
                gap: 30px;
                justify-content: space-between;

                .column {
                    width: 100%;

                    .section-title {
                        margin-bottom: 0;
                    }

                    .content {
                        .icon {
                            max-width: 120px;
                        }

                        .text {
                            text-align: left;
                            font-size: $font-size-18px;
                        }
                    }
                }
            }
        }
    }

    .section-cards {
        .wrapper {
            .card {
                min-height: 215px;

                &:nth-child(1) {
                    min-height: 220px;
                }

                .card-header {
                    flex-wrap: wrap;
                    padding: 4rem 0;

                    .icon {
                        .section-title {
                            font-size: $font-size-36px;
                        }
                    }
                }

                .card-content {
                    flex-direction: column;
                    gap: 25px;

                    .image {
                        flex-basis: 100%;
                        width: 100%;

                        img {
                            object-fit: contain;
                            aspect-ratio: 1 / 1;
                        }
                    }

                    .description {
                        flex-basis: 100%;
                        font-size: $font-size-16px;
                    }

                    .products {
                        grid-template-columns: repeat(2, 1fr);
                        justify-content: center;
                    }
                }
            }
        }
    }

    .section-movie {
        .wrapper {
            .section-header {
                .section-title {
                    font-size: $font-size-48px;
                }

                .section-subtitle {
                    font-size: $font-size-16px;
                    max-width: 100%;
                }
            }

            .section-content {
                flex-direction: column;

                .movie {
                    flex: 100%;
                    width: 100%;
                }

                .movie-info {
                    min-height: fit-content;
                    width: 100%;

                    .insider {
                        padding: 1rem;
                        width: 100%;
                    }
                }
            }
        }
    }

    .section-find-us {
        flex-direction: column;

        .wrapper {
            padding-top: 2rem;
            justify-content: flex-start;

            .insider {
                flex-basis: 100%;
                padding: 0;
            }
        }
    }

    .section-more-inspiration {
        padding-bottom: 0;

        .wrapper {
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            position: relative;

            .section-title {
                font-size: $font-size-24px;
                margin-bottom: 1rem;
            }

            .swiper-button-container {
                position: relative;
                bottom: 10px;
                right: 50%;
                padding: 2rem 0;

                .swiper-button-next {
                    left: auto;
                    right: -50px;
                }

                .swiper-button-prev {
                    right: var(--swiper-navigation-sides-offset, 20px);
                    left: auto;
                }
            }
        }

        .swiperMoreInspiration {
            .swiper-slide {
                .post-thumbnail {
                    max-height: 200px;
                }
            }
        }
    }
}

@media (max-width: $media-mobile) {
    .main-swipper {
        .left-right {
            gap: 0;

            .left-side {
                flex-basis: 100%;
                min-width: 100%;
                padding: 3rem 0 4rem 0;

                .main-content {
                    max-width: 100%;
                    margin: 0 auto;
                    padding: 0 15px;

                    .main-title {
                        font-size: $font-size-36px;
                        line-height: 1.2;
                    }

                    .main-subtitle {
                        font-size: $font-size-16px;
                    }

                    .main-button {
                        padding: 15px;
                    }
                }
            }
        }

        .button-wrapper {
            right: 15px;
            bottom: 50%;

            .swiper-button,
            .swiper-pagination {
                width: 35px;
                height: 35px;
            }

            .swiper-pagination {
                font-size: $font-size-18px;
            }
        }
    }

    .section-category {
        .grid {
            grid-template-columns: repeat(2, 1fr);
            grid-column-gap: 25px;
            grid-row-gap: 25px;

            .category-item {
                max-height: 60px;
                min-height: unset;

                svg,
                hr {
                    display: none;
                }

                &:last-of-type {
                    max-height: 60px;
                    min-height: unset;
                }

                .category-link {
                    overflow: clip;

                    .category-content {
                        height: 100%;

                        .insider {
                            height: 100%;
                        }
                    }
                }
            }
        }
    }

    .opinie-section {
        padding: 40px 0;

        .section-title {
            font-size: $font-size-24px;
        }

        .ti-widget.ti-goog {
            .ti-reviews-container {
                .ti-controls {
                    .ti-next {
                        right: 35% !important;
                    }

                    .ti-prev {
                        left: 35% !important;
                    }
                }
            }
        }
    }

    .section-gray {
        .left-right {
            .inner-left {
                .section-title {
                    font-size: $font-size-32px;
                }

                .dd-black-border-button {
                    text-align: center;
                }
            }

            .inner-right {
                .inner-text,
                .icon {
                    flex-basis: calc(50% - 15px);
                }
            }
        }
    }

    .section-for {
        .category-section {
            .category-content {
                .category-text {
                    font-size: $font-size-28px;
                    line-height: unset;
                    flex-wrap: wrap;

                    .main-button {
                        padding: 15px;
                    }
                }

                .product-list {
                    .woocommerce {
                        ul.products {
                            grid-template-columns: 1fr;
                        }
                    }
                }
            }
        }
    }

    @media screen and (max-width: 380px) {
        .section-for {
            .category-section {
                .category-content {
                    .swiper-button-container {
                        position: relative;
                        top: unset;
                    }

                    .swiper-wrapper {
                        padding-top: 2rem;
                    }
                }
            }
        }
    }

    .section-quality {
        .wrapper {
            flex-direction: column;
            align-items: flex-start;
            gap: 0;

            .insider {
                .column,
                .column:not(:first-of-type) {
                    flex-basis: 100%;
                    width: 100%;

                    .content {
                        flex-wrap: wrap;
                        justify-content: center;
                    }

                    .section-title {
                        font-size: $font-size-28px;
                        line-height: 110%;
                    }
                }
            }
        }
    }

    .section-inspired-posts {
        .wrapper {
            display: flex;
            justify-content: space-between;

            .section-title {
                font-size: $font-size-24px;
            }

            .swiper-button-container {
                top: 25px;

                @media screen and (max-width: 550px) {
                    position: relative;
                    padding: 4.5rem 0;
                    right: 50px;
                }
            }
        }
    }

    .section-cards {
        .wrapper {
            .card {
                &:nth-child(1) {
                    min-height: 210px;
                }

                .card-header {
                    flex-wrap: wrap;
                    padding: 2rem 0;

                    .icon {
                        width: 100%;

                        .section-title {
                            font-size: $font-size-24px;
                        }
                    }
                }

                .card-content {
                    .products {
                        display: grid;
                        justify-content: unset;
                        grid-template-columns: 1fr;
                    }
                }
            }
        }
    }

    @media screen and (max-width: 550px) {
        .section-cards {
            .wrapper {
                .card {
                    min-height: 270px;

                    &:nth-child(1) {
                        min-height: 270px;
                    }

                    .card-header {
                        .main-button {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    .section-find-us {
        .wrapper {
            .insider {
                .section-title {
                    font-size: $font-size-32px;
                    line-height: 1.2;
                }
            }
        }
    }
}

@media (max-width: $media-mobile-sm) {
    .main-swipper {
        .button-wrapper {
            bottom: 45%;
        }
    }

    .section-gray {
        .left-right {
            .inner-right {
                .inner-text,
                .icon {
                    flex-basis: 100%;
                }
            }
        }
    }

    .section-inspired-posts {
        .wrapper {
            .section-title {
                font-size: $font-size-20px;
            }

            .swiper-button-container {
                top: 30px;
            }
        }
    }

    .section-bestseller {
        .wrapper {
            .swiper-button-container {
                .swiper-button-next {
                    right: 30%;
                    left: auto;
                }

                .swiper-button-prev {
                    right: auto;
                    left: 30%;
                }
            }
        }
    }

    .opinie-section {
        padding-top: 0;

        .section-title {
            font-size: $font-size-24px;
        }

        .ti-widget.ti-goog {
            .ti-reviews-container {
                .ti-controls {
                    .ti-next {
                        right: 25% !important;
                    }

                    .ti-prev {
                        left: 25% !important;
                    }
                }
            }
        }
    }

    .section-inspired {
        .inspired-wrapper {
            .swiperInspired {
                .swiper-button-container {
                    .swiper-button-next {
                        right: 30%;
                    }

                    .swiper-button-prev {
                        left: 30%;
                    }
                }
            }
        }
    }
}
