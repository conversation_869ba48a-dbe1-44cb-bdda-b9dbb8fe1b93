<?php

namespace DD\FitospaTheme;

use WC_Product_Variation;

require_once get_template_directory() . '/woocommerce/product-variations.php';

class HeaderMiniCart
{
    public static function init(): void
    {
        add_action('wp_ajax_woocommerce_get_mini_cart_content', function (){
            echo self::getMiniCartContent();
            wp_die();
        });
        add_action('wp_ajax_nopriv_woocommerce_get_mini_cart_content', function (){
            echo self::getMiniCartContent();
            wp_die();
        });

        // Funkcja do dodawania produktu do koszyka i zwracania HTML mini-koszyka
        add_action('wp_ajax_woocommerce_add_to_cart', function (){
            self::custom_ajax_add_to_cart();
        });
        add_action('wp_ajax_nopriv_woocommerce_add_to_cart', function (){
            self::custom_ajax_add_to_cart();
        });

        // Usuwanie produktów z koszyka
        add_action('wp_ajax_remove_from_cart', function (){
            self::ajax_remove_from_cart();
        });
        add_action('wp_ajax_nopriv_remove_from_cart', function (){
            self::ajax_remove_from_cart();
        });

        // Zmiana liczby przedmiotów w koszyku
        add_action( 'wp_ajax_update_cart', function (){
            self::ajax_update_cart();
        });
        add_action( 'wp_ajax_nopriv_update_cart', function (){
            self::ajax_update_cart();
        });
    }

    public static function getMiniCartContent(): string
    {
        if (WC()->cart->is_empty()) {
            return '<p>Koszyk jest pusty.</p>';
        }

        ob_start();

        ?>
        <ul class="cart-items">
            <?php foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) :
                $_product = $cart_item['data'];
                $product_id = $cart_item['product_id'];

                $product_price = apply_filters('woocommerce_cart_item_price', $_product->get_price_html(), $cart_item, $cart_item_key);

                $variation_attributes = null;
                $product_image_url = get_the_post_thumbnail_url( $product_id, 'thumbnail' );
                if ($_product->is_type('variation')) {
                    /** @var WC_Product_Variation $_product */
                    $variation_attributes = $_product->get_attribute_summary();
                }
                ?>
                <li class="cart-item" data-item-key="<?php echo esc_attr( $cart_item_key ); ?>">
                    <div class="cart-item-header">
                        <div class="cart-item-image">
                            <?php
                            $image_url = $_product->is_type('variation') && $_product->get_image_id()
                                ? wp_get_attachment_image_url($_product->get_image_id(), 'thumbnail')
                                : get_the_post_thumbnail_url($product_id, 'thumbnail');

                            echo '<img src="' . esc_url($image_url) . '" alt="' . esc_attr($_product->get_name()) . '">';
                            ?>
                        </div>
                        <div class="cart-item-details">
                            <span class="product-name">
                                <a href="<?php echo esc_url(get_permalink($product_id)); ?>">
                                    <?php echo esc_html($_product->is_type('variation') ? wc_get_product($_product->get_parent_id())?->get_name() : $_product->get_name()); ?>
                                </a>
                            </span>
                            <div class="cart-item-column">
                                <?php if (!empty($variation_attributes)) : ?>
                                    <span class="product-attributes">
                                        <?php echo $variation_attributes ?>
                                    </span>
                                <?php endif; ?>
                                <div class="cart-price" style="width: <?php echo $_product->is_type('variation') ? 'auto' : '100%'; ?>;">
                                    <span class="product-price"><?php echo $product_price; ?></span>
                                </div>
                            </div>
                            <div class="cart-item-footer">
                                <div class="cart-quantity">
                                    <input type="number" name="cart[<?php echo $cart_item_key; ?>][qty]"
                                           value="<?php echo $cart_item['quantity']; ?>" min="1"
                                           max="<?php echo $_product->get_max_purchase_quantity(); ?>"
                                           onkeyup="if(this.value > <?php echo $_product->get_max_purchase_quantity(); ?>) this.value = 1;">
                                </div>
                                <div class="cart-remove">
                                    <a href="javascript:void(0);" class="remove-item"><i class="fa fa-trash"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>


                </li>
            <?php endforeach; ?>
        </ul>

        <div class="cart-summary">
            <span class="cart-summary-left">Suma:</span>
            <span class="cart-summary-right"><?php echo WC()->cart->get_cart_total(); ?></span>
        </div>

        <?php if (self::dd_get_mini_cart_free_shipping_amount() > 0) { ?>
        <div class="dd-mini-cart-free-shipping">
            <img src="<?php echo get_parent_theme_file_uri() . '/public/delivery-icon.png'?>"/>
            <p>Wydaj jeszcze <strong><?php echo wc_price(self::dd_get_mini_cart_free_shipping_amount()) ?></strong> aby otrzymać darmową wysyłkę!</p>
        </div>
        <?php } ?>

        <div class="cart-actions">
            <a href="<?php echo wc_get_checkout_url(); ?>" class="checkout-button"><?php _e( 'Złóż zamówienie', 'woocommerce' ); ?></a>
            <a href="<?php echo get_permalink( wc_get_page_id( 'shop' ) ); ?>" class="continue-shopping"><?php _e( 'Kontynuuj zakupy', 'woocommerce' ); ?></a>
        </div>
        <?php

        return ob_get_clean();
    }

    private static function custom_ajax_add_to_cart(): void
    {
        // Pobierz ID produktu i ilość
        $product_id = (int) $_POST['product_id'];
        $quantity = (int) $_POST['quantity'];

        // Dodaj produkt do koszyka
        $added = WC()->cart->add_to_cart($product_id, $quantity);

        if ($added) {
            wp_send_json(array('html' => self::getMiniCartContent()));
        } else {
            wp_send_json(array('error' => 'Nie udało się dodać produktu do koszyka.'));
        }

        wp_die();
    }

    private static function dd_get_mini_cart_free_shipping_amount(): float
    {
        $freeShippingThreshold = defined('DD_B2B_FREE_SHIPPING_INCLUDING_TAX_VALUE') ? DD_B2B_FREE_SHIPPING_INCLUDING_TAX_VALUE : DD_FREE_SHIPPING_INCLUDING_TAX_VALUE;

        return $freeShippingThreshold - (
                WC()->cart->get_cart_contents_total() + WC()->cart->get_cart_contents_tax()
            );
    }

    private static function ajax_remove_from_cart(): void
    {
        if ( ! isset( $_POST['cart_item_key'] ) ) {
            wp_send_json_error( 'Brak klucza produktu' );
            return;
        }

        $cart_item_key = sanitize_text_field( $_POST['cart_item_key'] );
        WC()->cart->remove_cart_item( $cart_item_key );
        WC()->cart->calculate_totals();

        wp_send_json_success(array('html' => self::getMiniCartContent()));
    }

    private static function ajax_update_cart() {
        if ( ! isset( $_POST['cart_item_key'] ) || ! isset( $_POST['quantity'] ) ) {
            wp_send_json_error( 'Brak odpowiednich danych' );
            return;
        }

        $cart_item_key = sanitize_text_field( $_POST['cart_item_key'] );
        $quantity = absint( $_POST['quantity'] );

        if ( $quantity > 0 ) {
            WC()->cart->set_quantity( $cart_item_key, $quantity );
        } else {
            WC()->cart->remove_cart_item( $cart_item_key );
        }

        WC()->cart->calculate_totals();

        wp_send_json_success(array('html' => self::getMiniCartContent()));
    }
}