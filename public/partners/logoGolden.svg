<svg id="logoGolden" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="256.011" height="127.057" viewBox="0 0 256.011 127.057">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_110" data-name="Path 110" d="M0-189.921H256.011V-62.864H0Z" transform="translate(0 189.921)" fill="#c6ad79"/>
    </clipPath>
  </defs>
  <g id="Layer_3_copy" data-name="Layer 3 copy">
    <path id="Path_56" data-name="Path 56" d="M-3.749-4.762H-5.532V-9.3H-9.54v4.536h-1.786v-9.625H-9.54v4.363h4.007v-4.363h1.784Z" transform="translate(118.761 45.765)" fill="#c6ad79"/>
    <g id="Group_2" data-name="Group 2" clip-path="url(#clip-path)">
      <path id="Path_57" data-name="Path 57" d="M-3.916-2.495a5.492,5.492,0,0,0-.87-3.245A2.529,2.529,0,0,0-6.924-6.918,2.533,2.533,0,0,0-9.062-5.739a5.536,5.536,0,0,0-.87,3.245A5.421,5.421,0,0,0-9.075.712a2.556,2.556,0,0,0,2.151,1.2A2.558,2.558,0,0,0-4.771.712a5.43,5.43,0,0,0,.854-3.207m1.915,0a4.827,4.827,0,0,1-1.452,3.6A4.776,4.776,0,0,1-6.924,2.531,4.725,4.725,0,0,1-10.4,1.1a4.876,4.876,0,0,1-1.43-3.6A4.847,4.847,0,0,1-10.38-6.114,4.772,4.772,0,0,1-6.924-7.537,4.883,4.883,0,0,1-4-6.579,4.765,4.765,0,0,1-2-2.495" transform="translate(129.382 38.709)" fill="#c6ad79"/>
      <path id="Path_58" data-name="Path 58" d="M-3.377-.357H-5.9v8.9H-7.682v-8.9H-10.2V-1.08h6.826Z" transform="translate(138.939 32.458)" fill="#c6ad79"/>
      <path id="Path_59" data-name="Path 59" d="M-2.947-4.762H-8.9v-9.625h5.955v.723H-7.116v3.524h3.874v.723H-7.116v3.936h4.169Z" transform="translate(146.499 45.765)" fill="#c6ad79"/>
      <path id="Path_60" data-name="Path 60" d="M-2.947-4.762H-8.9v-9.625h1.784v8.905h4.172Z" transform="translate(155.243 45.765)" fill="#c6ad79"/>
      <path id="Path_61" data-name="Path 61" d="M-2.364,0-1.6,3.976l4.014-.5L-1.13,5.428.588,9.09-2.364,6.321-5.317,9.09-3.6,5.428-7.141,3.473l4.014.5Z" transform="translate(113.032 15.921)" fill="#c6ad79"/>
      <path id="Path_62" data-name="Path 62" d="M-2.364,0-1.6,3.976l4.013-.5L-1.128,5.428.587,9.09-2.364,6.321-5.32,9.09-3.6,5.428-7.143,3.473l4.012.5Z" transform="translate(125.342 15.921)" fill="#c6ad79"/>
      <path id="Path_63" data-name="Path 63" d="M-2.364,0-1.6,3.976l4.017-.5L-1.129,5.428.591,9.09-2.364,6.321-5.317,9.09-3.6,5.428-7.141,3.473l4.017.5Z" transform="translate(137.646 15.921)" fill="#c6ad79"/>
      <path id="Path_64" data-name="Path 64" d="M-2.366,0-1.6,3.976l4.015-.5L-1.134,5.428.584,9.09l-2.95-2.769L-5.322,9.09-3.6,5.428-7.148,3.473l4.015.5Z" transform="translate(149.959 15.921)" fill="#c6ad79"/>
      <path id="Path_65" data-name="Path 65" d="M-5.843-5.924H-9.2v-7.869l-3.229,7.869h-.329l-3.154-7.52v7.52h-1.741V-17.9h3.14l2.7,6.453L-9.13-17.9h3.287Z" transform="translate(89.295 100.314)" fill="#c6ad79"/>
      <path id="Path_66" data-name="Path 66" d="M-4.509-3.094A6.909,6.909,0,0,0-5.25-6.623,2.258,2.258,0,0,0-7.257-7.86,2.254,2.254,0,0,0-9.266-6.623a6.9,6.9,0,0,0-.747,3.529A6.915,6.915,0,0,0-9.266.428,2.251,2.251,0,0,0-7.257,1.674,2.255,2.255,0,0,0-5.25.428a6.926,6.926,0,0,0,.742-3.522M-.9-3.074A5.972,5.972,0,0,1-2.913,1.6,6.269,6.269,0,0,1-7.257,3.159,6.22,6.22,0,0,1-11.6,1.6,6.041,6.041,0,0,1-13.621-3.11a6.022,6.022,0,0,1,2.018-4.7A6.237,6.237,0,0,1-7.257-9.346,6.282,6.282,0,0,1-2.913-7.789,6.015,6.015,0,0,1-.9-3.074" transform="translate(98.687 91.522)" fill="#c6ad79"/>
      <path id="Path_67" data-name="Path 67" d="M-4.672-5.924H-6.615l-5.722-8.748v8.748h-1.778V-17.9h3.687l3.958,6.123V-17.9h1.8Z" transform="translate(113.537 100.314)" fill="#c6ad79"/>
      <path id="Path_68" data-name="Path 68" d="M-4.807-.8H-7.981V9.566h-3.354V-.8h-3.188V-2.408h9.716Z" transform="translate(124.564 84.824)" fill="#c6ad79"/>
      <path id="Path_69" data-name="Path 69" d="M-3.148-3.42-4.692-7.711-6.1-3.42ZM2.223,1.642H-1.336L-2.6-1.9H-6.615l-1.193,3.54h-1.7L-5.4-10.332H-2.16Z" transform="translate(128.334 92.748)" fill="#c6ad79"/>
      <path id="Path_70" data-name="Path 70" d="M-4.672-5.924H-6.618l-5.719-8.748v8.748h-1.778V-17.9h3.685l3.961,6.123V-17.9h1.8Z" transform="translate(145.608 100.314)" fill="#c6ad79"/>
      <path id="Path_71" data-name="Path 71" d="M-3.148-3.42-4.689-7.711-6.1-3.42ZM2.224,1.642H-1.333L-2.6-1.9H-6.614l-1.193,3.54H-9.512l4.107-11.974h3.247Z" transform="translate(151.4 92.748)" fill="#c6ad79"/>
      <path id="Path_72" data-name="Path 72" d="M-5.25,0-8.68,8.86l-1.828-4.621L-12.27,8.86-15.861,0h1.7l2.343,6,.967-2.544L-12.27,0h1.682l2.316,5.893L-6.086,0Z" transform="translate(80.256 102.275)" fill="#c6ad79"/>
      <path id="Path_73" data-name="Path 73" d="M-1.915-3.1H-5.787V-9.355h3.872v.468H-4.628v2.294h2.519v.468H-4.628v2.56h2.713Z" transform="translate(82.182 114.021)" fill="#c6ad79"/>
      <path id="Path_74" data-name="Path 74" d="M-1.914-3.1H-5.783V-9.355h1.157v5.79h2.711Z" transform="translate(87.862 114.021)" fill="#c6ad79"/>
      <path id="Path_75" data-name="Path 75" d="M-1.915-3.1h-3.87V-9.355h1.159v5.79h2.711Z" transform="translate(92.896 114.021)" fill="#c6ad79"/>
      <path id="Path_76" data-name="Path 76" d="M-2.406-3.1h-.557L-6.711-8.081V-3.1h-.557V-9.355H-6.2l3.241,4.323V-9.355h.557Z" transform="translate(99.388 114.021)" fill="#c6ad79"/>
      <path id="Path_77" data-name="Path 77" d="M-1.915-3.1h-3.87V-9.355h3.87v.468H-4.626v2.294h2.519v.468H-4.626v2.56h2.711Z" transform="translate(104.838 114.021)" fill="#c6ad79"/>
      <path id="Path_78" data-name="Path 78" d="M-1.972-2.28A1.817,1.817,0,0,1-2.558-.888a2.07,2.07,0,0,1-1.472.55A2.176,2.176,0,0,1-5.959-1.356l.46-.326a1.347,1.347,0,0,0,1.216.835,1.107,1.107,0,0,0,.822-.318,1.107,1.107,0,0,0,.311-.816,1.034,1.034,0,0,0-.395-.85A4.582,4.582,0,0,0-4.567-3.35,2.665,2.665,0,0,1-5.6-4.011,1.508,1.508,0,0,1-5.95-5.027a1.776,1.776,0,0,1,.545-1.332,1.954,1.954,0,0,1,1.41-.528,1.842,1.842,0,0,1,1.85,1.1l-.413.277a1.2,1.2,0,0,0-1.2-.911,1,1,0,0,0-.747.293,1.046,1.046,0,0,0-.286.763.909.909,0,0,0,.306.7,2.764,2.764,0,0,0,.821.48,4.392,4.392,0,0,1,1.233.712A1.576,1.576,0,0,1-1.972-2.28" transform="translate(110.359 111.417)" fill="#c6ad79"/>
      <path id="Path_79" data-name="Path 79" d="M-1.974-2.28A1.818,1.818,0,0,1-2.561-.888a2.069,2.069,0,0,1-1.474.55,2.18,2.18,0,0,1-1.93-1.018l.463-.326a1.348,1.348,0,0,0,1.218.835,1.1,1.1,0,0,0,.819-.318,1.1,1.1,0,0,0,.311-.816,1.029,1.029,0,0,0-.393-.85A4.537,4.537,0,0,0-4.572-3.35a2.735,2.735,0,0,1-1.036-.661,1.527,1.527,0,0,1-.345-1.016,1.774,1.774,0,0,1,.547-1.332A1.943,1.943,0,0,1-4-6.887a1.847,1.847,0,0,1,1.851,1.1l-.414.277a1.2,1.2,0,0,0-1.2-.911,1,1,0,0,0-.747.293,1.052,1.052,0,0,0-.286.763.912.912,0,0,0,.3.7,2.8,2.8,0,0,0,.821.48,4.332,4.332,0,0,1,1.233.712A1.568,1.568,0,0,1-1.974-2.28" transform="translate(115.681 111.417)" fill="#c6ad79"/>
      <path id="Path_80" data-name="Path 80" d="M-1.718-.637q0-.732-.783-.731a.743.743,0,0,0-.477.152.45.45,0,0,0-.185.354.794.794,0,0,0,.092.33l.635.912q.718-.5.719-1.018m1.5,6.316L-2.734,1.96a2.5,2.5,0,0,0-1.2,2.065,2.344,2.344,0,0,0,.607,1.67,2,2,0,0,0,1.526.65A2.172,2.172,0,0,0-.222,5.679m2.237,1.26H.586L.148,6.277A3.238,3.238,0,0,1-2.1,7.1a2.975,2.975,0,0,1-2.2-.889,2.964,2.964,0,0,1-.887-2.184A3.644,3.644,0,0,1-3.335,1.058l-.477-.7a1.4,1.4,0,0,1-.277-.78A1.328,1.328,0,0,1-3.547-1.47a2.022,2.022,0,0,1,1.338-.454,1.568,1.568,0,0,1,1.126.434A1.427,1.427,0,0,1-.633-.411,2.158,2.158,0,0,1-1.745,1.339L.387,4.567a4.8,4.8,0,0,0,.08-.728,5.272,5.272,0,0,0-.24-1.4H.892a4.348,4.348,0,0,1,.223,1.335A4.766,4.766,0,0,1,.876,5.31Z" transform="translate(123.586 103.985)" fill="#c6ad79"/>
      <path id="Path_81" data-name="Path 81" d="M-2.726-3.151a2.519,2.519,0,0,1-.809,1.923,2.867,2.867,0,0,1-2.041.755,3,3,0,0,1-2.66-1.4l.635-.45A1.858,1.858,0,0,0-5.918-1.175a1.518,1.518,0,0,0,1.131-.438,1.53,1.53,0,0,0,.429-1.125A1.429,1.429,0,0,0-4.9-3.916a6.465,6.465,0,0,0-1.416-.716,3.818,3.818,0,0,1-1.43-.913,2.079,2.079,0,0,1-.478-1.4,2.456,2.456,0,0,1,.754-1.841,2.7,2.7,0,0,1,1.948-.73A2.55,2.55,0,0,1-2.966-7.994l-.569.383A1.662,1.662,0,0,0-5.192-8.869a1.387,1.387,0,0,0-1.032.4,1.442,1.442,0,0,0-.4,1.054,1.253,1.253,0,0,0,.425.969,3.959,3.959,0,0,0,1.137.664,6.171,6.171,0,0,1,1.7.981,2.176,2.176,0,0,1,.635,1.645" transform="translate(139.64 111.609)" fill="#c6ad79"/>
      <path id="Path_82" data-name="Path 82" d="M-2.206-1.323A1.882,1.882,0,0,0-4.351-3.441h-.714V.53h.714Q-2.2.53-2.206-1.323m1.6-.106A2.33,2.33,0,0,1-1.337.407a2.927,2.927,0,0,1-2.007.654H-5.065V4.651h-1.6V-4h3.1a3.177,3.177,0,0,1,2.186.7A2.378,2.378,0,0,1-.6-1.429" transform="translate(145.887 106.273)" fill="#c6ad79"/>
      <path id="Path_83" data-name="Path 83" d="M-2.177-2.958-3.434-6.111l-1.26,3.153ZM.671-.1H-1.039l-.9-2.306H-4.918L-5.805-.1h-.771l3.549-8.837Z" transform="translate(151.938 111.023)" fill="#c6ad79"/>
      <path id="Path_84" data-name="Path 84" d="M-18.131-43.785l-36.644-88.5h8.87l24.386,57.962,23.85-57.962H17.448Z" transform="translate(72.814 148.56)" fill="#c6ad79"/>
      <path id="Path_85" data-name="Path 85" d="M242.332-71.1h-1v-94.347h1Z" transform="translate(-79.881 181.821)" fill="#c6ad79"/>
      <path id="Path_86" data-name="Path 86" d="M-6.8-10.991H-20.543V-33.205H-6.8v1.666h-9.628v8.13h8.945v1.669h-8.945v9.08H-6.8Z" transform="translate(100.828 83.646)" fill="#c6ad79"/>
      <path id="Path_87" data-name="Path 87" d="M-7.792-10.991h-4.625l-5.954-11.261A10,10,0,0,0-13.5-23.528a3.79,3.79,0,0,0,1.489-3.35q0-4.9-5.723-4.9h-1.694v20.784h-4.113V-33.205h7.279q8.365,0,8.368,6.5a4.9,4.9,0,0,1-1.5,3.774A6.549,6.549,0,0,1-13.677-21.4Z" transform="translate(121.448 83.646)" fill="#c6ad79"/>
      <path id="Path_88" data-name="Path 88" d="M-7.339-5.469q0-4.557-2.358-6.972t-6.8-2.416h-1.561V4.021h2.445q3.8,0,5.458-1.461Q-7.343.079-7.339-5.469m4.419.546A10.289,10.289,0,0,1-5.9,2.764a10.741,10.741,0,0,1-7.875,2.927h-8.4V-16.523h5.442q6.533,0,10.169,3.094A10.568,10.568,0,0,1-2.92-4.923" transform="translate(139.061 66.964)" fill="#c6ad79"/>
      <path id="Path_89" data-name="Path 89" d="M-6.8-10.991H-20.545V-33.205H-6.8v1.666h-9.628v8.13H-7.48v1.669h-8.948v9.08H-6.8Z" transform="translate(159.917 83.646)" fill="#c6ad79"/>
      <path id="Path_90" data-name="Path 90" d="M-2.887-3.854q0-2.484-3.208-2.485H-7.568v4.6h1.859a3.634,3.634,0,0,0,2.138-.5,1.862,1.862,0,0,0,.683-1.618m-.579-5.009q0-2.223-2.806-2.222h-1.3v4.248h1.555A3,3,0,0,0-4.136-7.27a1.96,1.96,0,0,0,.67-1.593m1.845,4.948q0,2.739-3.736,2.739H-8.723V-11.643h2.356a4.964,4.964,0,0,1,2.966.769A2.419,2.419,0,0,1-2.343-8.82a2.246,2.246,0,0,1-.513,1.485,2.049,2.049,0,0,1-1.38.732q2.615.515,2.615,2.689" transform="translate(179.478 57.179)" fill="#c6ad79"/>
      <path id="Path_91" data-name="Path 91" d="M271.3-111.4h-1.155v-10.45H271.3Z" transform="translate(-89.417 167.391)" fill="#c6ad79"/>
      <path id="Path_92" data-name="Path 92" d="M-4.156,0l-6.719,9.888h6.591v.562h-8.272L-5.839.558h-6.124V0Z" transform="translate(196.84 45.536)" fill="#c6ad79"/>
      <path id="Path_93" data-name="Path 93" d="M-3.831-5.306l-7.117-9.252v8.995h-.626V-16.029h.963l6.155,8.045v-8.045h.625Z" transform="translate(206.823 61.565)" fill="#c6ad79"/>
      <path id="Path_94" data-name="Path 94" d="M-3.006-5.179H-9.082V-15.645h5.917v.558H-7.926v4.3h4.473v.529H-7.926v4.522h4.92Z" transform="translate(215.809 61.181)" fill="#c6ad79"/>
      <path id="Path_95" data-name="Path 95" d="M-2.862-3.807a3.245,3.245,0,0,1-.879,2.352,3.014,3.014,0,0,1-2.245.9,2.843,2.843,0,0,1-2.66-1.557l.525-.32A2.3,2.3,0,0,0-5.965-1a1.773,1.773,0,0,0,1.414-.622A2.489,2.489,0,0,0-4.017-3.29a2.107,2.107,0,0,0-.367-1.331,3.367,3.367,0,0,0-1.277-.861l-.654-.291a5.069,5.069,0,0,1-1.709-1.07A2.289,2.289,0,0,1-8.6-8.46a2.941,2.941,0,0,1,.875-2.17A2.909,2.909,0,0,1-5.586-11.5,2.681,2.681,0,0,1-3.2-10.027l-.465.273a1.955,1.955,0,0,0-1.8-1.3,2.042,2.042,0,0,0-1.491.6,2,2,0,0,0-.61,1.485,1.915,1.915,0,0,0,.482,1.361,4.123,4.123,0,0,0,1.379.85l.674.287A4.563,4.563,0,0,1-3.373-5.411a2.427,2.427,0,0,1,.511,1.6" transform="translate(223.743 56.81)" fill="#c6ad79"/>
      <path id="Path_96" data-name="Path 96" d="M-3.466-1.443A2.26,2.26,0,0,1-4.332.408a3.683,3.683,0,0,1-2.37.7,3.119,3.119,0,0,1-.4-.019L-3.53,6.106H-4.957L-8.708.658h1.555a2.583,2.583,0,0,0,1.8-.582,2.034,2.034,0,0,0,.643-1.592q0-2.284-2.838-2.283H-9.316V6.106h-1.155V-4.361h3.126a4.624,4.624,0,0,1,2.861.777,2.543,2.543,0,0,1,1.019,2.14" transform="translate(181.226 69.132)" fill="#c6ad79"/>
      <path id="Path_97" data-name="Path 97" d="M-4.346-2.713A5.81,5.81,0,0,0-5.413-6.369a3.279,3.279,0,0,0-2.7-1.378,3.285,3.285,0,0,0-2.7,1.378,5.814,5.814,0,0,0-1.065,3.656A5.78,5.78,0,0,0-10.816.934,3.294,3.294,0,0,0-8.115,2.3,3.288,3.288,0,0,0-5.413.934,5.777,5.777,0,0,0-4.346-2.713m1.218,0A5.466,5.466,0,0,1-4.595,1.206,4.694,4.694,0,0,1-8.129,2.753a4.691,4.691,0,0,1-3.536-1.547,5.466,5.466,0,0,1-1.466-3.918A5.533,5.533,0,0,1-11.64-6.657,4.623,4.623,0,0,1-8.129-8.195,4.627,4.627,0,0,1-4.618-6.657,5.54,5.54,0,0,1-3.129-2.713" transform="translate(193.103 72.743)" fill="#c6ad79"/>
      <path id="Path_98" data-name="Path 98" d="M-3.569-2.578A4.574,4.574,0,0,0-4.82-6.017,4.914,4.914,0,0,0-8.379-7.225H-9.627V2.12h1.49A4.455,4.455,0,0,0-4.763.877,4.775,4.775,0,0,0-3.569-2.578m1.268.193A5.148,5.148,0,0,1-3.65,1.191,4.873,4.873,0,0,1-7.464,2.68h-3.319V-7.787h2.2a6.756,6.756,0,0,1,4.623,1.46A4.979,4.979,0,0,1-2.3-2.384" transform="translate(203.66 72.558)" fill="#c6ad79"/>
      <path id="Path_99" data-name="Path 99" d="M-4.156,0l-6.717,9.891h6.588v.561h-8.27L-5.84.562h-6.123V0Z" transform="translate(215.533 64.771)" fill="#c6ad79"/>
      <path id="Path_100" data-name="Path 100" d="M320.873-82.651h-1.154V-93.1h1.154Z" transform="translate(-105.827 157.874)" fill="#c6ad79"/>
      <path id="Path_101" data-name="Path 101" d="M-3.83-5.306l-7.117-9.251v8.993h-.624V-16.031h.961l6.155,8.047v-8.047h.625Z" transform="translate(230.355 80.802)" fill="#c6ad79"/>
      <path id="Path_102" data-name="Path 102" d="M-2.951-3.442-4.809-8.251-6.732-3.442ZM-.321.293H-1.475L-2.792-2.945H-6.926L-8.208.293h-.708L-4.634-10.4Z" transform="translate(237.732 74.946)" fill="#c6ad79"/>
      <path id="Path_103" data-name="Path 103" d="M-4.156,0l-6.713,9.89h6.586v.562h-8.272L-5.84.561h-6.121V0Z" transform="translate(182.027 84.008)" fill="#c6ad79"/>
      <path id="Path_104" data-name="Path 104" d="M-3.569-2.578A4.554,4.554,0,0,0-4.818-6.015a4.907,4.907,0,0,0-3.56-1.211H-9.626V2.12h1.49A4.459,4.459,0,0,0-4.761.876,4.768,4.768,0,0,0-3.569-2.578m1.268.191A5.145,5.145,0,0,1-3.647,1.19,4.883,4.883,0,0,1-7.461,2.68h-3.32V-7.787h2.195A6.767,6.767,0,0,1-3.959-6.329,4.986,4.986,0,0,1-2.3-2.387" transform="translate(191.17 91.796)" fill="#c6ad79"/>
      <path id="Path_105" data-name="Path 105" d="M-3.466-1.442A2.26,2.26,0,0,1-4.331.411a3.685,3.685,0,0,1-2.374.7,3.761,3.761,0,0,1-.4-.016l3.57,5.017H-4.956L-8.707.658h1.555A2.579,2.579,0,0,0-5.356.08a2.033,2.033,0,0,0,.643-1.592q0-2.285-2.839-2.285H-9.317V6.11h-1.154V-4.357h3.127a4.628,4.628,0,0,1,2.861.776A2.55,2.55,0,0,1-3.466-1.442" transform="translate(202.242 88.366)" fill="#c6ad79"/>
      <path id="Path_106" data-name="Path 106" d="M-4.347-2.713A5.8,5.8,0,0,0-5.416-6.366a3.273,3.273,0,0,0-2.7-1.379,3.274,3.274,0,0,0-2.7,1.379,5.8,5.8,0,0,0-1.066,3.653A5.779,5.779,0,0,0-10.816.936a3.283,3.283,0,0,0,2.7,1.371A3.282,3.282,0,0,0-5.416.936,5.776,5.776,0,0,0-4.347-2.713m1.216,0A5.461,5.461,0,0,1-4.6,1.206,4.685,4.685,0,0,1-8.132,2.755a4.693,4.693,0,0,1-3.536-1.549,5.465,5.465,0,0,1-1.465-3.918,5.537,5.537,0,0,1,1.49-3.942,4.629,4.629,0,0,1,3.511-1.54A4.634,4.634,0,0,1-4.62-6.655,5.546,5.546,0,0,1-3.131-2.713" transform="translate(214.122 91.978)" fill="#c6ad79"/>
      <path id="Path_107" data-name="Path 107" d="M-6.083,0-10.2,10.723l-2.022-5.467-2.051,5.467L-18.377,0h1.2l3.218,8.4,1.447-3.926L-14.179,0h1.187l3.078,8.416L-6.723,0Z" transform="translate(230.828 84.008)" fill="#c6ad79"/>
      <path id="Path_108" data-name="Path 108" d="M340.548-53.9h-1.155V-64.348h1.155Z" transform="translate(-112.339 148.356)" fill="#c6ad79"/>
      <path id="Path_109" data-name="Path 109" d="M-3.007-5.179H-9.086V-15.646h5.917v.56H-7.931v4.3h4.474v.528H-7.931v4.522h4.923Z" transform="translate(240.98 99.655)" fill="#c6ad79"/>
    </g>
  </g>
</svg>
