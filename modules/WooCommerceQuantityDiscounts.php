<?php

namespace DD\FitospaTheme;

class WooCommerceQuantityDiscounts
{
    private const META_KEY_QUANTITY_DISCOUNTS = '_quantity_discounts';
    private const META_KEY_VARIATION_QUANTITY_DISCOUNTS = '_variation_quantity_discounts';

    public static function init(): void
    {
        add_filter('woocommerce_product_data_tabs', [self::class, 'addProductDataTab']);
        add_action('woocommerce_product_data_panels', [self::class, 'addProductDataPanel']);
        add_action('woocommerce_process_product_meta', [self::class, 'saveProductFields']);

        add_action('woocommerce_product_after_variable_attributes', [self::class, 'addVariationFields'], 10, 3);
        add_action('woocommerce_save_product_variation', [self::class, 'saveVariationFields'], 10, 2);

        add_action('woocommerce_single_product_summary', [self::class, 'displayQuantityDiscountTable'], 25);
        add_action('dd_after_product_price', [self::class, 'displayQuantityDiscountTable']);
        add_action('woocommerce_after_single_product_summary', [self::class, 'displayQuantityDiscountTableFallback'], 5);

        add_filter('woocommerce_get_price_html', [self::class, 'modifyPriceDisplay'], 10, 2);
        add_action('woocommerce_before_calculate_totals', [self::class, 'applyQuantityDiscounts'], 20);
        add_filter('woocommerce_cart_item_price', [self::class, 'displayCartItemPrice'], 10, 3);
        add_filter('woocommerce_cart_item_subtotal', [self::class, 'displayCartItemSubtotal'], 10, 3);
        add_action('woocommerce_add_to_cart', [self::class, 'storeOriginalPrice'], 10, 6);
        add_action('woocommerce_after_cart_item_quantity_update', [self::class, 'updateCartItemQuantity'], 10, 4);
        add_action('woocommerce_cart_loaded_from_session', [self::class, 'ensureOriginalPricesStored']);

        add_action('wp_enqueue_scripts', [self::class, 'enqueueScripts']);
    }

    public static function addProductDataTab($tabs): array
    {
        $tabs['quantity_discounts'] = [
            'label' => 'Rabaty ilościowe',
            'target' => 'quantity_discounts_product_data',
            'class' => ['show_if_simple', 'show_if_variable'],
        ];
        return $tabs;
    }

    public static function addProductDataPanel(): void
    {
        global $post;
        $quantity_discounts = get_post_meta($post->ID, self::META_KEY_QUANTITY_DISCOUNTS, true);
        if (!is_array($quantity_discounts)) {
            $quantity_discounts = [];
        }

        echo '<div id="quantity_discounts_product_data" class="panel woocommerce_options_panel">';
        echo '<div class="options_group">';
        echo '<h3>Rabaty ilościowe dla produktu</h3>';

        echo '<div class="quantity-discount-admin-panel">';
        echo '<div class="role-selector-section">';
        echo '<label for="discount-role-selector">Wybierz rolę użytkownika:</label>';
        echo '<select id="discount-role-selector">';
        echo '<option value="">-- Wybierz rolę --</option>';

        $roles = self::getUserRoles();
        foreach ($roles as $role_key => $role_name) {
            echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role_name) . '</option>';
        }

        echo '</select>';
        echo '</div>';

        echo '<div class="discount-ranges-container" style="display: none;">';
        echo '<h4 class="selected-role-title"></h4>';
        echo '<div class="discount-ranges-list"></div>';
        echo '<button type="button" class="button add-discount-range">Dodaj nowy zakres</button>';
        echo '</div>';
        echo '</div>';

        foreach ($roles as $role_key => $role_name) {
            echo '<div class="quantity-discount-role-data" data-role="' . esc_attr($role_key) . '" style="display: none;">';
            $role_discounts = $quantity_discounts[$role_key] ?? [];

            foreach ($role_discounts as $index => $tier) {
                echo '<input type="hidden" name="quantity_discounts[' . $role_key . '][' . $index . '][min_qty]" value="' . esc_attr($tier['min_qty']) . '" class="tier-min-qty">';
                echo '<input type="hidden" name="quantity_discounts[' . $role_key . '][' . $index . '][max_qty]" value="' . esc_attr($tier['max_qty']) . '" class="tier-max-qty">';
                echo '<input type="hidden" name="quantity_discounts[' . $role_key . '][' . $index . '][discount]" value="' . esc_attr($tier['discount']) . '" class="tier-discount">';
            }
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
    }

    public static function addVariationFields($loop, $variation_data, $variation): void
    {
        $variation_discounts = get_post_meta($variation->ID, self::META_KEY_VARIATION_QUANTITY_DISCOUNTS, true);
        if (!is_array($variation_discounts)) {
            $variation_discounts = [];
        }

        echo '<div class="variation-quantity-discounts" data-loop="' . $loop . '">';
        echo '<h4>Rabaty ilościowe dla wariantu</h4>';

        echo '<div class="variation-discount-admin-panel">';
        echo '<div class="variation-role-selector-section">';
        echo '<label for="variation-discount-role-selector-' . $loop . '">Wybierz rolę użytkownika:</label>';
        echo '<select id="variation-discount-role-selector-' . $loop . '" class="variation-discount-role-selector">';
        echo '<option value="">-- Wybierz rolę --</option>';

        $roles = self::getUserRoles();
        foreach ($roles as $role_key => $role_name) {
            echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role_name) . '</option>';
        }

        echo '</select>';
        echo '</div>';

        echo '<div class="variation-discount-ranges-container" style="display: none;">';
        echo '<h5 class="variation-selected-role-title"></h5>';
        echo '<div class="variation-discount-ranges-list"></div>';
        echo '<button type="button" class="button add-variation-discount-range">Dodaj nowy zakres</button>';
        echo '</div>';
        echo '</div>';

        foreach ($roles as $role_key => $role_name) {
            echo '<div class="variation-quantity-discount-role-data" data-role="' . esc_attr($role_key) . '" data-loop="' . $loop . '" style="display: none;">';
            $role_discounts = $variation_discounts[$role_key] ?? [];

            foreach ($role_discounts as $index => $tier) {
                echo '<input type="hidden" name="variation_quantity_discounts[' . $loop . '][' . $role_key . '][' . $index . '][min_qty]" value="' . esc_attr($tier['min_qty']) . '" class="variation-tier-min-qty">';
                echo '<input type="hidden" name="variation_quantity_discounts[' . $loop . '][' . $role_key . '][' . $index . '][max_qty]" value="' . esc_attr($tier['max_qty']) . '" class="variation-tier-max-qty">';
                echo '<input type="hidden" name="variation_quantity_discounts[' . $loop . '][' . $role_key . '][' . $index . '][discount]" value="' . esc_attr($tier['discount']) . '" class="variation-tier-discount">';
            }
            echo '</div>';
        }

        echo '</div>';
    }

    public static function saveProductFields($post_id): void
    {
        if (isset($_POST['quantity_discounts'])) {
            $quantity_discounts = [];
            foreach ($_POST['quantity_discounts'] as $role => $tiers) {
                $quantity_discounts[$role] = [];
                foreach ($tiers as $tier) {
                    if (!empty($tier['min_qty']) && !empty($tier['discount'])) {
                        $quantity_discounts[$role][] = [
                            'min_qty' => intval($tier['min_qty']),
                            'max_qty' => !empty($tier['max_qty']) ? intval($tier['max_qty']) : 999999,
                            'discount' => floatval($tier['discount'])
                        ];
                    }
                }
                usort($quantity_discounts[$role], function($a, $b) {
                    return $a['min_qty'] - $b['min_qty'];
                });
            }
            update_post_meta($post_id, self::META_KEY_QUANTITY_DISCOUNTS, $quantity_discounts);
        }
    }

    public static function saveVariationFields($variation_id, $i): void
    {
        if (isset($_POST['variation_quantity_discounts'][$i])) {
            $variation_discounts = [];
            foreach ($_POST['variation_quantity_discounts'][$i] as $role => $tiers) {
                $variation_discounts[$role] = [];
                foreach ($tiers as $tier) {
                    if (!empty($tier['min_qty']) && !empty($tier['discount'])) {
                        $variation_discounts[$role][] = [
                            'min_qty' => intval($tier['min_qty']),
                            'max_qty' => !empty($tier['max_qty']) ? intval($tier['max_qty']) : 999999,
                            'discount' => floatval($tier['discount'])
                        ];
                    }
                }
                usort($variation_discounts[$role], function($a, $b) {
                    return $a['min_qty'] - $b['min_qty'];
                });
            }
            update_post_meta($variation_id, self::META_KEY_VARIATION_QUANTITY_DISCOUNTS, $variation_discounts);
        }
    }

    public static function displayQuantityDiscountTable(): void
    {
        global $product;

        if (!$product) {
            return;
        }

        $current_user = wp_get_current_user();
        $user_roles = $current_user->roles;
        $user_role = !empty($user_roles) ? $user_roles[0] : 'customer';

        if (!is_user_logged_in()) {
            $user_role = 'customer';
        }

        if ($product->is_type('variable')) {
            $variations = $product->get_available_variations();
            foreach ($variations as $variation_data) {
                $variation = wc_get_product($variation_data['variation_id']);
                $discounts = self::getQuantityDiscounts($variation->get_id(), $user_role, true);
                if (!empty($discounts)) {
                    echo '<div class="quantity-discount-table-wrapper" data-variation-id="' . $variation->get_id() . '" style="display: none;">';
                    self::renderDiscountTable($discounts);
                    echo '</div>';
                }
            }
        } else {
            $discounts = self::getQuantityDiscounts($product->get_id(), $user_role, false);
            if (!empty($discounts)) {
                echo '<div class="quantity-discount-table-wrapper">';
                self::renderDiscountTable($discounts);
                echo '</div>';
            }
        }
    }

    private static function renderDiscountTable($discounts): void
    {
        echo '<div class="quantity-discount-table">';
        echo '<h4>Rabaty przy większych ilościach</h4>';
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Ilość</th>';
        echo '<th>Rabat</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($discounts as $discount) {
            $qty_range = $discount['min_qty'];
            if ($discount['max_qty'] < 999999) {
                $qty_range .= '-' . $discount['max_qty'];
            } else {
                $qty_range .= '+';
            }

            echo '<tr>';
            echo '<td>' . $qty_range . ' szt.</td>';
            echo '<td>-' . $discount['discount'] . '%</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }

    public static function displayQuantityDiscountTableFallback(): void
    {
        static $displayed = false;

        if ($displayed) {
            return;
        }

        $displayed = true;
        self::displayQuantityDiscountTable();
    }

    public static function modifyPriceDisplay($price_html, $product): string
    {
        if (is_admin() || !is_woocommerce()) {
            return $price_html;
        }

        if (is_cart() || is_checkout()) {
            return $price_html;
        }

        $cart_item_key = self::findProductInCart($product->get_id());
        if (!$cart_item_key) {
            return $price_html;
        }

        $cart_item = WC()->cart->get_cart_item($cart_item_key);
        if (isset($cart_item['quantity_discount_applied']) && $cart_item['quantity_discount_applied'] &&
            isset($cart_item['original_base_price']) && isset($cart_item['discount_percent']) &&
            $cart_item['discount_percent'] > 0) {

            $original_price = $cart_item['original_base_price'];
            $discounted_price = $product->get_price();

            // Sprawdź czy ceny rzeczywiście się różnią (z tolerancją na drobne różnice)
            if (abs($original_price - $discounted_price) > 0.01) {
                $price_html = '<del>' . wc_price($original_price) . '</del> ';
                $price_html .= '<ins>' . wc_price($discounted_price) . '</ins>';
            }
        }

        return $price_html;
    }

    public static function storeOriginalPrice($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data): void
    {
        $product = $variation_id ? wc_get_product($variation_id) : wc_get_product($product_id);
        if ($product) {
            // Pobierz aktualną cenę produktu (po zastosowaniu innych wtyczek, ale przed rabatami ilościowymi)
            WC()->cart->cart_contents[$cart_item_key]['original_base_price'] = $product->get_price();
        }
    }

    public static function updateCartItemQuantity($cart_item_key, $quantity, $old_quantity, $cart): void
    {
        WC()->cart->calculate_totals();
    }

    public static function ensureOriginalPricesStored(): void
    {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if (!isset($cart_item['original_base_price'])) {
                $is_variation = isset($cart_item['variation_id']) && $cart_item['variation_id'] > 0;
                $product_id = $is_variation ? $cart_item['variation_id'] : $cart_item['product_id'];

                // Pobierz świeży produkt z bazy danych
                $original_product = wc_get_product($product_id);

                if ($original_product) {
                    // Użyj aktualnej ceny produktu
                    WC()->cart->cart_contents[$cart_item_key]['original_base_price'] = $original_product->get_price();
                }
            }
        }
    }

    public static function applyQuantityDiscounts($cart): void
    {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        $current_user = wp_get_current_user();
        $user_roles = $current_user->roles;
        $user_role = !empty($user_roles) ? $user_roles[0] : 'customer';

        if (!is_user_logged_in()) {
            $user_role = 'customer';
        }

        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];

            $is_variation = isset($cart_item['variation_id']) && $cart_item['variation_id'] > 0;
            $product_id = $is_variation ? $cart_item['variation_id'] : $cart_item['product_id'];

            $discounts = self::getQuantityDiscounts($product_id, $user_role, $is_variation);
            $discount_percent = self::getDiscountForQuantity($discounts, $quantity);

            if ($discount_percent > 0) {
                if (!isset($cart_item['original_base_price'])) {
                    // Użyj aktualnej ceny produktu (po zastosowaniu innych wtyczek)
                    $cart_item['original_base_price'] = $product->get_price();
                }

                $base_price = $cart_item['original_base_price'];
                $discounted_price = $base_price * (1 - $discount_percent / 100);
                $product->set_price($discounted_price);

                WC()->cart->cart_contents[$cart_item_key]['quantity_discount_applied'] = true;
                WC()->cart->cart_contents[$cart_item_key]['discount_percent'] = $discount_percent;
                WC()->cart->cart_contents[$cart_item_key]['original_base_price'] = $base_price;
            } else {
                // Usuń flagi rabatu jeśli nie ma rabatu
                WC()->cart->cart_contents[$cart_item_key]['quantity_discount_applied'] = false;
                WC()->cart->cart_contents[$cart_item_key]['discount_percent'] = 0;
            }
        }
    }

    private static function getQuantityDiscounts($product_id, $user_role, $is_variation): array
    {
        $meta_key = $is_variation ? self::META_KEY_VARIATION_QUANTITY_DISCOUNTS : self::META_KEY_QUANTITY_DISCOUNTS;
        $discounts = get_post_meta($product_id, $meta_key, true);

        if (!is_array($discounts) || !isset($discounts[$user_role])) {
            return [];
        }

        return $discounts[$user_role];
    }

    private static function getDiscountForQuantity($discounts, $quantity): float
    {
        foreach ($discounts as $discount) {
            if ($quantity >= $discount['min_qty'] && $quantity <= $discount['max_qty']) {
                return $discount['discount'];
            }
        }
        return 0;
    }

    private static function findProductInCart($product_id): ?string
    {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if ($cart_item['product_id'] == $product_id ||
                (isset($cart_item['variation_id']) && $cart_item['variation_id'] == $product_id)) {
                return $cart_item_key;
            }
        }
        return null;
    }

    private static function getUserRoles(): array
    {
        $roles = wp_roles()->roles;
        $user_roles = [];

        foreach ($roles as $role_key => $role) {
            $user_roles[$role_key] = $role['name'];
        }

        return $user_roles;
    }

    public static function displayCartItemPrice($price_html, $cart_item, $cart_item_key): string
    {
        if (isset($cart_item['quantity_discount_applied']) && $cart_item['quantity_discount_applied'] &&
            isset($cart_item['original_base_price']) && isset($cart_item['discount_percent']) &&
            $cart_item['discount_percent'] > 0) {

            $original_price = $cart_item['original_base_price'];
            $discounted_price = $cart_item['data']->get_price();

            // Sprawdź czy ceny rzeczywiście się różnią (z tolerancją na drobne różnice)
            if (abs($original_price - $discounted_price) > 0.01) {
                $price_html = '<del>' . wc_price($original_price) . '</del> ';
                $price_html .= '<ins>' . wc_price($discounted_price) . '</ins>';
            }
        }

        return $price_html;
    }

    public static function displayCartItemSubtotal($subtotal_html, $cart_item, $cart_item_key): string
    {
        if (isset($cart_item['quantity_discount_applied']) && $cart_item['quantity_discount_applied'] &&
            isset($cart_item['original_base_price']) && isset($cart_item['discount_percent']) &&
            $cart_item['discount_percent'] > 0) {

            $quantity = $cart_item['quantity'];
            $original_price = $cart_item['original_base_price'];
            $discounted_price = $cart_item['data']->get_price();

            $original_subtotal = $original_price * $quantity;
            $discounted_subtotal = $discounted_price * $quantity;

            // Sprawdź czy ceny rzeczywiście się różnią (z tolerancją na drobne różnice)
            if (abs($original_subtotal - $discounted_subtotal) > 0.01) {
                $subtotal_html = '<del>' . wc_price($original_subtotal) . '</del> ';
                $subtotal_html .= '<ins>' . wc_price($discounted_subtotal) . '</ins>';
            }
        }

        return $subtotal_html;
    }

    public static function enqueueScripts(): void
    {
        if (is_product()) {
            wp_enqueue_script(
                'quantity-discounts',
                get_template_directory_uri() . '/assets/js/quantity-discounts.js',
                ['jquery'],
                DD_THEME_VERSION,
                true
            );

            wp_enqueue_style(
                'quantity-discounts',
                get_template_directory_uri() . '/assets/css/quantity-discounts.css',
                [],
                DD_THEME_VERSION
            );
        }
    }
}
