.quantity-discount-table-wrapper {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
}

.quantity-discount-table h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-gray);
    font-family: 'Lato', sans-serif;
}

.quantity-discount-table table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quantity-discount-table th,
.quantity-discount-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.quantity-discount-table th {
    background-color: #f3f4f6;
    font-weight: 600;
    color: var(--text-gray);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quantity-discount-table td {
    font-size: 14px;
    color: var(--text-gray);
}

.quantity-discount-table tr:last-child td {
    border-bottom: none;
}

.quantity-discount-table tr:hover {
    background-color: #f9fafb;
}

.quantity-discount-role-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.quantity-discount-role-section h4,
.quantity-discount-role-section h5 {
    margin: 0 0 10px 0;
    color: #333;
}

.quantity-discount-tiers {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quantity-discount-tier {
    display: flex;
    gap: 10px;
    align-items: center;
}

.quantity-discount-tier input {
    padding: 5px 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.quantity-discount-tier::before {
    content: "Przedział " counter(tier-counter) ": ";
    font-weight: bold;
    margin-right: 10px;
}

.quantity-discount-tiers {
    counter-reset: tier-counter;
}

.quantity-discount-tier {
    counter-increment: tier-counter;
}

.variation-quantity-discounts {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

@media (max-width: 768px) {
    .quantity-discount-table-wrapper {
        margin: 15px 0;
        padding: 15px;
    }

    .quantity-discount-table th,
    .quantity-discount-table td {
        padding: 8px 12px;
        font-size: 13px;
    }

    .quantity-discount-tier {
        flex-direction: column;
        align-items: stretch;
    }

    .quantity-discount-tier input {
        width: 100%;
    }
}
